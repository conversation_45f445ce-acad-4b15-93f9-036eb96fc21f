import request from '@/utils/request'
const baseURL = 'Combo/'

//获取体检项目
export function ListItem(data) {
  return request({
    url: baseURL + 'ListItem',
    method: 'get',
    params: data,
  })
}
//获取体检套餐
export function ListCombo(data) {
  return request({
    url: baseURL + 'ListCombo',
    method: 'get',
    params: data,
  })
}
//套餐项目关联
export function ListContact(data) {
  return request({
    url: baseURL + 'ListContact',
    method: 'get',
    params: data,
  })
}
//项目新增修改
export function SaveItemData(data) {
  return request({
    url: baseURL + 'SaveItemData',
    method: 'post',
    data,
  })
}
//项目移除
export function RemoveItemData(data) {
  return request({
    url: baseURL + 'RemoveItemData',
    method: 'post',
    data,
  })
}
//项目新增修改
export function SaveExamData(data) {
  return request({
    url: baseURL + 'SaveExamData',
    method: 'post',
    data,
  })
}
//项目移除
export function RemoveExamData(data) {
  return request({
    url: baseURL + 'RemoveExamData',
    method: 'post',
    data,
  })
}
//移除
export function RemoveContect(data) {
  return request({
    url: baseURL + 'RemoveContect',
    method: 'post',
    data,
  })
}
//配置套餐项目
export function InsertItems(data) {
  return request({
    url: baseURL + 'InsertItems',
    method: 'post',
    data,
  })
}
//批量导入项目信息
export function addExcelItem(data) {
  return request({
    url: baseURL + 'addExcelItem',
    method: 'post',
    data: data,
  })
} 
//上传图片信息
export function SaveExamImage(data) {
  return request({
    url: baseURL + 'SaveExamImage',
    method: 'post',
    data: data,
  })
}
