<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="专家名称" prop="expertName">
        <el-input
          v-model="queryParams.expertName"
          placeholder="请输入专家名称"
          clearable
          style="width: 240px"
          @change="getList"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="getList"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <!-- 新增导入导出 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" @click="handleAdd">新增</el-button>
      </el-col>
    </el-row>
    <!-- 表格数据       
      @row-click="clickRow"-->
    <el-table
      ref="table"
      :row-key="rowKey"
      :data="expertsList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
        :reserve-selection="true"
      />
      <el-table-column
        label="专家姓名"
        align="center"
        prop="expertName"
        :show-overflow-tooltip="true"
      >
        <!-- <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expirationDate, "{y}-{m}-{d}") }}</span>
        </template> -->
      </el-table-column>
      <el-table-column
        label="专家标签"
        align="center"
        prop="expertLabel"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="主要成就"
        align="center"
        prop="expertSpeciality"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="详细介绍"
        align="center"
        prop="expertIntroduce"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="照片" align="center">
        <template slot-scope="scope">
          <span
            ><img :src="baseApi + scope.row.imgUrl" alt="" class="img"
          /></span>
        </template>
      </el-table-column>
      <el-table-column label="显示状态" align="center">
        <!-- inactive-value="" -->
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="0"
            inactive-value="1"
            active-color="#13ce66"
            inactive-color="#ff4949"
            @change="handleStatusChange(scope.row)"
          >
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >编辑</el-button
          >

          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="expertsTotal > 0"
      :total="expertsTotal"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增对话框         :disabled="thisDisabled"-->
    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="专家姓名：" prop="expertName">
              <el-input
                v-model="form.expertName"
                placeholder="请输入专家姓名"
                clearable
                style="width: 200px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专家标签：">
              <el-input
                v-model="form.expertLabel"
                placeholder="请输入专家标签"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="主要成就：">
              <el-input
                v-model="form.expertSpeciality"
                placeholder="请输入专家主要成就"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 6 }"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="详细介绍：">
              <el-input
                v-model="form.expertIntroduce"
                placeholder="请输入详细介绍"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 6 }"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注：">
              <el-input
                v-model="form.remark"
                type="textarea"
                placeholder="请输入内容"
                :autosize="{ minRows: 2, maxRows: 6 }"
              ></el-input> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="排序：" v-show="this.form.id != undefined">
              <!-- <el-input
                v-model="form.sort"
                placeholder="请输入内容"
              ></el-input>  -->
              <el-input-number
                size="small"
                :min="1"
                v-model="form.sort"
              ></el-input-number> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="上传照片：">
              <el-upload
                class="upload-demo"
                :multiple="multiple2"
                action="#"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :before-remove="beforeRemove"
                :before-upload="beforeUpload"
                accept=".jpg,.png"
                :http-request="UploadFile"
                :limit="limit"
                :on-exceed="handleExceed"
                :file-list="fileList"
              >
                <el-button size="small" type="primary">点击上传</el-button>
              </el-upload>
            </el-form-item></el-col
          >
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  CommonFileUpload,
  GetExpertsList,
  ToDelExperts,
  ToDetailExperts,
  UpdateExperts,
  StatusChange,
} from "@/api/experts/expert";
import env from "@/utils/ApiConfig";
// import ImportExcel from "@/components/excel/ImportExcel";
// import ExportExcel from "@/components/excel/exportExcel";
// import { utils } from "@/mixin.js";
export default {
  //   components: {
  //     ExportExcel,
  //     ImportExcel,
  //   },
  name: "inbound",
  props: {
    limit: {
      type: Number,
      default: 1,
    },
    multiple2: {
      type: Boolean,
      default: false,
    },
  },
  //   dicts: ["sys_yes_no"],
  data() {
    return {
      fileList: [],
      positionList: [],
      expertsList: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      open: false,
      //存放区域id
      depositId: undefined,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      expertsTotal: 0,
      title: "",
      baseApi: "",
      loading: false,
      // 日期范围
      dateRange: [],
      //Excel数据导出
      exportExcelData: [],
      //数据导出格式
      columnMap: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      paramsBody: {
        // 选中数组
        ids: [],
      },
      // 表单参数
      form: {
        expertName: "",
        expertLabel: "",
        expertSpeciality: "",
        expertIntroduce: "",
        remark: "",
        id: undefined,
        sort: 0,
      },
      // 暂时取消表单校验
      rules: {
        expertName: [
          { required: true, message: "请输入专家姓名", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.baseApi = env.get_base_url();
  },
  //   mixins: [utils],
  methods: {
    /** 查询列表 addNewDateRange*/
    getList() {
      GetExpertsList(this.queryParams).then((response) => {
        this.loading = false;
        this.expertsTotal = response.data.expertsTotal;
        this.expertsList = response.data.expertsList;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.thisDisabled = false;
      this.open = true;
      this.title = "新增";
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.paramsBody.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      this.open = true;
      this.thisDisabled = true;
      this.title = "修改专家信息";
      ToDetailExperts(id).then((response) => {
        this.form = response.data.expertsList[0];
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // if (this.form.id != undefined) {
          //   this.toSubmit();
          // } else {
          this.toSubmit();
          //   this.open = false;
          // }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm('是否确认删除专家为"' + row.expertName + '"的数据项？')
        .then(function () {
          return ToDelExperts(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
          this.loading = false;
        })
        .catch(() => {});
    },
    rowKey(row) {
      return row.id;
    },
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row);
    },
    // 状态修改
    handleStatusChange(row) {
      let text = row.status === "1" ? "禁用" : "正常";
      this.$modal
        .confirm(
          `确认要将专家姓名为"${row.expertName}"的用户设为"${text}"状态吗？`
        )
        .then(() => {
          return StatusChange(row.id);
        })
        .then(() => {
          this.$modal.msgSuccess("改变状态成功");
          this.getList();
        })
        .catch(() => {
          row.status = row.status === "0" ? "1" : "0";
        });
    },

    // 上传
    async UploadFile(file) {
      const maxFileSize = 10 * 1024 * 1024; // 10MB
      if (file.file.size > maxFileSize) {
        this.$message.error("文件大小超过限制！最大为10M！");
        this.clearFileList();
        return;
      } else {
        this.fileList.push(file.file);
      }
    },
    toSubmit() {
      this.loading = true;
      let formData = new FormData();
      this.fileList.forEach((item) => {
        formData.append("file", item);
      });
      formData.append("remark", this.form.remark);
      formData.append("id", this.form.id);
      formData.append("expertName", this.form.expertName);
      formData.append("expertLabel", this.form.expertLabel);
      formData.append("expertSpeciality", this.form.expertSpeciality);
      formData.append("expertIntroduce", this.form.expertIntroduce);
      if (this.form.sort != undefined) {
        formData.append("sort", this.form.sort);
      }

      if (this.form.id != undefined) {
        UpdateExperts(formData).then((response) => {
          this.$modal.msgSuccess("修改成功");
          this.open = false;
          this.getList();
        });
      } else {
        if (this.fileList.length == 0) {
          this.$message.error("请选择文件");
          return;
        }
        CommonFileUpload(formData).then((res) => {
          if (res.data.errno == 200) {
            this.open = false;
            this.loading = false;
            this.dialogVisible = false;
            this.$modal.msgWarning(res.data.message);
            this.getList();
          }
        });
      }
      this.clearFileList();
    },
    beforeUpload(file) {},
    // 文件移除
    handleRemove(file, fileList) {
      this.fileList = this.fileList.filter((item) => {
        return item.uid != file.uid;
      });
    },
    handlePreview(file) {},
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      );
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    //清除文件
    clearFileList() {
      this.fileList = [];
    },
  },
};
</script>
<style lang="scss" scoped>
.el-container {
  max-height: 600px;
}

.qrcodeCon {
  text-align: center;
}

.qrcodeUrl {
  padding: 15px 0;
}

.qrcodeDownload {
  text-align: center;
}
.img {
  width: 100px;
  height: auto;
}
</style>
  