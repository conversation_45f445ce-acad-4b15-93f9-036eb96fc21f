import request from '@/utils/request'

export function GetDictLabelTree(type) {
  return request({
    url: 'PreventionManagementQueen/GetDictLabelTree?type=' + type,
    method: 'get',
  })
}

export function SaveHealthManagements(param) {
  return request({
    url: 'PreventionManagementQueen/SavePreventionManagements',
    method: 'post',
    data: param
  })
}

export function QueryHealthManagementsTypeAsPolicy(param) {
  return request({
    url: 'PreventionManagementQueen/QueryPreventionManagements',
    method: 'get',
    params: param
  })
}

export function DeleteHealthManagements(id) {
  return request({
    url: 'PreventionManagementQueen/DeletePreventionManagements?id='+id,
    method: 'post',
  })
}

export function UpdateHealthManagements(param) {
  return request({
    url: 'PreventionManagementQueen/UpdatePreventionManagements',
    method: 'post',
    data: param
  })
}
