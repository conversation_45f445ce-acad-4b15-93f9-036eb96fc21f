<template>
  <div class="app-container">
    <el-form :model="queueFrom" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="标题:" prop="title">
        <el-input v-model="queueFrom.title" placeholder="请输入标题名称" clearable style="width: 240px"
        />
      </el-form-item>
      <el-form-item label="主题:" prop="title">
        <el-select v-model="queueFrom.typeTwo" filterable placeholder="请选择">
          <el-option
            v-for="item in typeNameList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" plain icon="el-icon-search" size="mini" @click="getDataList">搜索
        </el-button>
        <el-button type="primary" plain icon="el-icon-refresh" size="mini" @click="resets">重置</el-button>
      </el-form-item>
    </el-form>

    <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addButton" v-hasPermi="['system:role:add']">
      新增
    </el-button>

    <!--    列表-->
    <el-table :data="pageList">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="标题" prop="title" align="center" width="250"/>
      <el-table-column label="文本内容" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope" sortable>
          <el-button size="mini" type="text" icon="el-icon-document" @click="newsParticulars(scope.row.html)">详情
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="置顶照片" align="center" width="150">
        <template slot-scope="scope">
          <el-popover placement="top" title="" trigger="hover">
            <el-image :src="scope.row.top_img_path" style="max-height: 600px; max-width: 900px;"></el-image>
            <el-image slot="reference" :src="scope.row.top_img_path" :alt="scope.row.top_img_path"></el-image>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.create_time) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.audit_status === '0'"><el-tag>通过</el-tag></span>
          <span v-else-if="scope.row.audit_status === '1'"><el-tag>审核中</el-tag></span>
          <span v-else-if="scope.row.audit_status === '2'"><el-tag type="danger">驳回</el-tag></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="newsUpdateButton3(scope.row)">标题修改
          </el-button>
          <br/>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="newsUpdateButton2(scope.row)">图片修改
          </el-button>
          <br/>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="newsUpdateButton1(scope.row)">文本修改
          </el-button>
          <br/>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="newsDelete(scope.row.id,scope.row.title)">
            资讯删除
          </el-button>
          <br/>
        </template>
      </el-table-column>
    </el-table>

    <!--    列表html详情查看 组件-->
    <el-dialog :visible.sync="newsLook" style="margin-top: -2%" width="80%" append-to-body>
      <html-particulars :key="key" :html-text="htmlText"></html-particulars>
    </el-dialog>

    <!--    新增 组件-->
    <el-dialog :visible.sync="addOpen" width="500px" append-to-body>
      <new-message-add :key="key" :dicts="typeNameList" :base-u-r-l="baseURL" :type="insertType"
                       @cancel-event="cancelEvent"
                       @refresh="refresh"
      ></new-message-add>
    </el-dialog>

    <!--标题修改-->
    <el-dialog :visible.sync="titleUpdateButton" width="30%" append-to-body>
      <new-message-add :key="key" :dicts="typeNameList" :type="titleUpdateType" :update-title-form="titleUpdateForm"
                       @cancel-event-two="cancelEventTwo"
      ></new-message-add>
    </el-dialog>

    <!--    照片修改-->
    <el-dialog :visible.sync="imageUpdateButton" width="30%" append-to-body>
      <new-message-add :key="key" :dicts="typeNameList" :base-u-r-l="baseURL" :type="imageUpdateType"
                       :update-title-form="titleUpdateForm"
                       @cancel-event-two="cancelEventTwo"
      ></new-message-add>
    </el-dialog>

    <!--文本域修改-->
    <el-dialog :visible.sync="htmlUpdateButton" style="margin-top: -1.5%" width="95%" append-to-body>
      <html-redact :key="key" :update-forms="titleUpdateForm" :base-u-r-l="baseURL" :type="htmlUpdateType"
                   @cancel-event-two="cancelEventTwo"
      ></html-redact>
    </el-dialog>
  </div>
</template>

<script>

import {
  DeleteArchiveInfoAfterById, getTypeName,
  QueryArchiveInfoAfterList
} from "../../api/archiveInfomation/archiveInfomation";

import htmlParticulars from './htmlParticulars.vue'
import newMessageAdd from './newMessageAdd.vue'
import htmlRedact from './htmlRedact.vue'
import env from '@/utils/ApiConfig'
import {GetDictLabelTree} from "../../api/prevention/manage";

export default {
  name: 'archiveInfomation',
  props: [],
  components: {
    htmlParticulars,
    newMessageAdd,
    htmlRedact
  },
  data() {
    return {
      queueFrom: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        title: '',
        typeTwo: ''
      },
      typeNameList: [],
      showSearch: true,
      addOpen: false,
      pageList: [],
      newsLook: false,
      htmlText: '',
      key: 0,
      titleUpdateButton: false,
      titleUpdateType: '2',
      titleUpdateForm: {},
      imageUpdateButton: false,
      htmlUpdateButton: false,
      baseURL: env.get_base_url(),
      insertType: '1',
      imageUpdateType: '3',
      htmlUpdateType: '2',
      logo: '',
      title: '添加主题',
      forms: {},
    }
  },
  created() {
    this.logo = this.baseURL + 'file/logo.png'
    this.dict();
    this.getDataList();
  },
  mounted() {
  },
  methods: {
    //获取类型
    dict() {
      GetDictLabelTree("archive-information").then(res => {
        this.typeNameList = res.data;
      })
    },
    // 分页查询
    getDataList() {
      this.pageList = []
      QueryArchiveInfoAfterList(this.queueFrom).then(res => {
        this.pageList = this.handleD(res.data.rows)
        this.queueFrom.total = res.data.total
      })
    },

    // 数据处理
    handleD(data) {
      let datas = []
      let logos = this.logo
      data.forEach(function (item, index) {
        if (item.top_img_path === null || item.top_img_path === '') {
          item.top_img_path = logos
        }
        datas.push(item)
      })
      return datas
    },

    //重置
    resets() {
      this.queueFrom = {
        pageNum: 1,
        pageSize: 10,
        title: '',
        typeTwo: ''
      }
    },
    //新闻详情
    newsParticulars(html) {
      this.htmlText = ''
      this.htmlText = html
      this.newsLook = true
    },

    // 标题修改事件
    newsUpdateButton3(row) {
      this.newsUpdateButton(row);
      this.titleUpdateButton = true
    },

    newsUpdateButton(data) {
      this.key += 1;
      this.titleUpdateForm = {};
      this.titleUpdateForm = data;
    },

    cancelEventTwo(data) {
      if (data) {
        this.titleUpdateButton = false
        this.imageUpdateButton = false
        this.htmlUpdateButton = false
        this.getDataList()
      }
    },

    // image修改事件
    newsUpdateButton2(row) {
      this.newsUpdateButton(row);
      this.imageUpdateButton = true
    },

    // html修改事件
    newsUpdateButton1(row) {
      this.newsUpdateButton(row);
      this.htmlUpdateButton = true
    },

    //新增按钮处理
    addButton() {
      this.addOpen = true
    },

    refresh(data) {
      if (data) {
        this.cancelEvent()
        this.getDataList()
      }
    },

    cancelEvent(data) {
      if (data) {
        this.addOpen = false
      }
    },

    //删除
    newsDelete(id, title) {
      this.$confirm('您确定要删除《' + title + '》的新闻信息,本次操作将遗留操作痕迹, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        DeleteArchiveInfoAfterById(id).then(res => {
          if (res.code === 200) {
            this.$message.success(res.message);
            this.getDataList();
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
  },
}
</script>

<style scoped>

</style>
