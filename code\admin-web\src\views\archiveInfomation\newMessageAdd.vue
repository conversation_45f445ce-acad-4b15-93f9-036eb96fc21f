<template>
  <div>
    <!--    新增文本-->
    <div v-if="type === '1'">
      <div>
        <el-form ref="form" :model="forms" :rules="rules" label-width="100px">
          <el-form-item label="标题：" prop="title">
            <el-input v-model="forms.title" placeholder="请输入标题名称"/>
          </el-form-item>
          <el-form-item label="主题：" prop="type">
            <el-select v-model="forms.type" filterable placeholder="请选择">
              <el-option
                v-for="item in dicts"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="置顶图片：">
            <el-upload list-type="picture-card" action="#" :multiple="false" :show-file-list="true"
                       :before-upload="beforeUpload" accept=".jpg, .png, .jpeg, .bmp,.webp" :limit="1"
                       :auto-upload="false"
                       :file-list="fileList" :on-remove="deleteFile" :on-change="onChange"
            >
              <i class="el-icon-plus"></i>
            </el-upload>
          </el-form-item>
          <el-form-item label="备注：">
            <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4}"
              placeholder="请输入内容"
              v-model="forms.remark"
            >
            </el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="nextStep">下一步</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </div>
      <!--    进行 文本编辑-->
      <div>
        <el-dialog :visible.sync="htmlButton" style="margin-top: -1.5%" width="95%" append-to-body>
          <html-redact :key="key" :form-data="forms" :type="htmlType" :base-u-r-l="baseURL" @html-button="htmlButtonMonitor" @refresh="refresh"></html-redact>
        </el-dialog>
      </div>
    </div>

<!--    标题修改-->
    <div v-if="type === '2'">
      <el-form ref="form" :model="updateForms" :rules="rules" label-width="80px">
        <el-form-item label="标题：" prop="title">
          <el-input v-model="updateForms.title" placeholder="请输入标题名称"/>
        </el-form-item>
        <el-form-item label="主题：" prop="type" label-width="80px">
          <el-select v-model="updateForms.type" filterable placeholder="请选择">
            <el-option
              v-for="item in dicts"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="newsUpdate">确 定</el-button>
        <el-button @click="cancelTwo">取 消</el-button>
      </div>
    </div>

<!--    图片修改-->
    <div v-if="type === '3'">
      <el-form ref="form" :model="updateForms" :rules="rules" label-width="60px">
        <el-form-item label="置顶图片" label-width="130px">
          <el-upload list-type="picture-card" action="#" :multiple="false" :show-file-list="true"
                     :before-upload="beforeUpload" accept=".jpg, .png, .jpeg, .bmp,.webp" :limit="1"
                     :auto-upload="false"
                     :file-list="fileList" :on-remove="deleteFile" :on-change="onChange2">
            <i class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="newsUpdate">确 定</el-button>
        <el-button @click="cancelTwo">取 消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { md5 } from '@/utils/md5'
import { UploadFile } from '@/api/news/release'
import htmlRedact from './htmlRedact.vue'
import {UpdateArchiveInfoAfter} from "../../api/archiveInfomation/archiveInfomation";
export default {
  name: 'newMessageAdd',
  props: ['dicts', 'baseURL','type','updateTitleForm',],
  components: {
    htmlRedact
  },
  data() {
    return {
      updateForms:{},
      forms: {},
      fileList: [],
      htmlButton: false,
      htmlType: '1',
      key: 0,
      rules: {
        title: [
          { required: true, message: '新闻标题不能为空', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '新闻主题不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.forms = {};
    this.updateForms = {};
    this.updateForms = this.updateTitleForm;
    console.log("2",this.updateForms)
  },
  mounted() {
  },
  methods: {
    // 确定事件
    newsUpdate(){
      UpdateArchiveInfoAfter(this.updateForms).then(res => {
        if (res.code === 200){
          this.$message.success(res.message);
          this.cancelTwo();
        }
      })
    },
    // 新增下一步事件
    nextStep() {
      this.htmlButton = true
      this.key += 1;
      this.cancel()
    },
    // 接收文本按钮
    htmlButtonMonitor(data) {
      if (data) {
        this.htmlButton = false;
      }
    },
    // 接收文本按钮 并 向上一级传参
    refresh(){
      this.htmlButton = false;
      this.$emit('refresh', true)
    },
    // 接收文本按钮 并 向上一级传参
    cancel() {
      this.$emit('cancel-event', true)
    },
    cancelTwo() {
      this.$emit('cancel-event-two', true)
    },
    // 选取完文件触发事件
    onChange(a, fileList) {
      let isLt2M = a.size / 1024 / 1024 < 0.5
      if (!isLt2M) {
        this.$message.error('置顶图片大小不能超过0.5MB!')
        this.fileList = []
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '休息一下,文件上传中(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.fileList = fileList
      const param = new FormData()
      param.append('file', a.raw)
      md5(a.raw).then(res => {
        param.append('md5', res)
        UploadFile(param).then(response => {
          this.forms.showPhotoWall = this.baseURL + response.data.path
          this.forms.showPhotoWallMd5 = response.data.md5
          loading.close()
        })
      })
    },
    onChange2(a, fileList) {
      let isLt2M = a.size / 1024 / 1024 < 0.5
      if (!isLt2M){
        this.$message.error('置顶图片大小不能超过0.5MB!')
        this.fileList = [];
        return;
      }
      const loading = this.$loading({
        lock: true,
        text: '休息一下,文件上传中(●'+ '◡' +'●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.fileList = fileList
      const param = new FormData()
      param.append('file', a.raw)
      md5(a.raw).then(res => {
        param.append("md5", res)
        UploadFile(param).then(response => {
          this.updateForms.top_img_path = this.baseURL + response.data.path;
          this.updateForms.top_img_md5 = response.data.md5;
          loading.close();
        })
      })
    },
    deleteFile(a, fileList) {
      // if (this.form.showPhotoWall != undefined) {
      //   delFile(this.form.showPhotoWall);
      // }
    },
    //上传成功后关闭弹窗并调用查询方法刷新页面数据
    beforeUpload(file) {
      // uploadFile(file).then(response => {
      //   this.forms.showPhotoWall = response.data
      // })
    }
  }
}
</script>

<style scoped lang="scss">
.dialog-footer{
  display: flex;
  justify-content: center;
}
</style>
