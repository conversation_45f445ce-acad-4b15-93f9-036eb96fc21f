const env = {
  api_url: 'https://www.aolijianhmc.com:9100/api',
  mappings: [
    { ip: 'localhost', url: 'http://localhost:9009/api' },
    { ip: '*********', url: 'http://*********:8046/api' },
    { ip: '*********', url: 'http://*********:8099/api' },
    { ip: '***********', url: 'http://***********:8088/api' },
    { ip: '************', url: 'http://************:8046/api' },
    { ip: '*************', url: 'http://*************:8099/api' },
    { ip: 'https://www.aolijianhmc.com', url: 'https://www.aolijianhmc.com:9100/api' },
  ],
  get_api_url() {
    let ip = window.location.hostname
    let mapping = this.mappings.find((t) => {
      return t.ip === ip
    })

    if (mapping !== undefined) {
      return mapping.url
    }
    return this.api_url
  },
  base_url: 'https://www.aolijianhmc.com:9100/',
  baseMappings: [
    { ip: 'localhost', url: 'http://localhost:9009/' },
    { ip: '*********', url: 'http://*********:8046/' },
    { ip: '*********', url: 'http://*********:8099/' },
    { ip: '***********', url: 'http://***********:8088/' },
    { ip: '************', url: 'http://************:8046/' },
    { ip: '*************', url: 'http://*************:8099/' },
    { ip: 'https://www.aolijianhmc.com', url: 'https://www.aolijianhmc.com:9100/' },
  ],
  get_base_url() {
    let ip = window.location.hostname
    let baseMapping = this.baseMappings.find((t) => {
      return t.ip === ip
    })
    if (baseMapping !== undefined) {
      return baseMapping.url
    }
    return this.base_url
  },
}
export default env
