import request from '@/utils/request'

//新闻类型
export function GetTypeName(query) {
  return request({
    url: 'ArchiveInfoAfter/GetTypeName',
    method: 'get',
    params: query
  })
}


export function QueryArchiveInfoAfterList(param) {
  return request({
    url: 'ArchiveInfoAfter/QueryArchiveInfoAfterList',
    method: 'get',
    params: param
  })
}

export function DeleteArchiveInfoAfterById(id) {
  return request({
    url: 'ArchiveInfoAfter/DeleteArchiveInfoAfterById?id='+id,
    method: 'post',
  })
}

export function UpdateArchiveInfoAfter(param) {
  return request({
    url: 'ArchiveInfoAfter/UpdateArchiveInfoAfter',
    method: 'post',
    data: param
  })
}


export function SaveArchiveInfoAfter(data) {
  return request({
    url: 'ArchiveInfoAfter/SaveArchiveInfoAfter',
    method: 'post',
    data: data
  })
}
