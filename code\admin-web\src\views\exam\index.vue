<template>
  <div class="app-container">
     <el-row :gutter="10">
      <el-col  :span="24" :xs="24">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="项目名称" prop="expertName">
        <el-input
          v-model="queryParams.comboName"
          placeholder="请输入项目名称"
          clearable
          style="width: 240px;"
          @change="getList"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="getList"
        >
          搜索
        </el-button>
      </el-form-item>
        <el-form-item>

        <el-button type="primary" @click="handleAdd"   size="mini">新增</el-button>

        </el-form-item>
      <el-form-item>
                <!-- 导入 -->
      <el-col :span="1.5">
        <import-excel
          :columnMap="columnMap"
          @resultData="resultData"
        ></import-excel>
      </el-col>
      </el-form-item>

    </el-form>
    </el-col>
    <!-- 新增 -->



    <!-- 表格数据
      @row-click="clickRow"-->
    <el-table ref="table" :data="comboList" border>
      <el-table-column
        label="检查项目"
        align="center"
        prop="item_name"
        :show-overflow-tooltip="true"
      >
        <!-- <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expirationDate, "{y}-{m}-{d}") }}</span>
        </template> -->
        <template slot-scope="scope">
          <span>
            <el-input
              v-model="scope.row.item_name"
              @change="itemChange(scope.row)"
               type="textarea"
            ></el-input>
          </span>
        </template>
      </el-table-column>

      <el-table-column
        label="基本内容"
        align="center"
        prop="item_content"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <span>
            <el-input
              v-model="scope.row.item_content"
              @change="itemChange(scope.row)"
               type="textarea"
            ></el-input>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="临床意义"
        align="center"
        prop="item_sense"
        :show-overflow-tooltip="true"
         type="textarea"
      >
        <template slot-scope="scope">
          <span>
            <el-input
             type="textarea"
              v-model="scope.row.item_sense"
              @change="itemChange(scope.row)"
            ></el-input>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="适用人群"
        align="center"
        prop="croud_suitable"
        :show-overflow-tooltip="true"

      >
        <template slot-scope="scope">
          <span>
            <el-select
              clearable
              v-model="scope.row.croud_suitable"
              @change="itemChange(scope.row)"
            >
              <el-option
                v-for="item in suitOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </span>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="expertsTotal > 0"
      :total="expertsTotal"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </el-row>
    <!-- 新增对话框         :disabled="thisDisabled"-->
    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="检查项目：" prop="itemName">
              <el-input
                v-model="form.itemName"
                placeholder="请输入检查项目"
                clearable
                style="width: 200px;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="基本内容">
              <el-input
                v-model="form.itemContent"
                placeholder="请输入基本内容"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="临床意义">
              <el-input
                v-model="form.itemSense"
                placeholder="请输入临床意义"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="适用人群">
              <el-radio-group v-model="form.croudSuitable">
                <el-radio
                  v-for="dict in suitOption"
                  :key="dict.value"
                  :label="dict.value"
                >
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ListItem, SaveItemData, RemoveItemData ,addExcelItem} from '@/api/exam/combo'
import env from '@/utils/ApiConfig'
import ImportExcel from "@/components/excel/ImportExcel";


export default {
  //   dicts: ["sys_yes_no"],
 components: {
    ImportExcel,

  },
  data() {
    return {

      clearFalse: false,
      suitOption: [
        { value: '通用', label: '通用' },
        { value: '男', label: '男' },
        { value: '女', label: '女' },
      ],
      comboList: [],
      fileList: [],
      positionList: [],
      expertsList: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      open: false,
      //存放区域id
      depositId: undefined,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      expertsTotal: 0,
      title: '',
      baseApi: '',
      loading: false,
      // 日期范围
      dateRange: [],
      //Excel数据导出
      exportExcelData: [],
      //数据导出格式
      columnMap: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      paramsBody: {
        // 选中数组
        ids: [],
      },
      // 表单参数
      form: {
        croudSuitable: '通用',
      },
      // 暂时取消表单校验
      rules: {
        expertName: [
          { required: true, message: '请输入专家姓名', trigger: 'blur' },
        ],
      },
    }
  },
  created() {
    this.getList()
    this.baseApi = env.get_base_url()
  },
  //   mixins: [utils],
  methods: {
    /** 查询列表 */
    getList() {
      ListItem(this.queryParams).then((res) => {
        if (res.code === 200) {
          this.comboList = res.data.rows
          this.columnMap =res.data.columnMap
        }
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {}
      this.resetForm('form')
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.thisDisabled = false
      this.open = true
      this.title = '新增'
    },
    itemChange(row) {
      let param = {
        ID: row.id,
        itemName: row.item_name,
        itemContent: row.item_content,
        itemSense: row.item_sense,
        croudSuitable: row.croud_suitable,
      }
      SaveItemData(param).then((res) => {
        if (res.code == 200) {
          this.$message({
            message: '新增成功',
            type: 'success',
            showClose: true,
          })
        }
      })
    },
    /** 提交按钮 */
    submitForm: function () {
      SaveItemData(this.form).then((res) => {
        if (res.code == 200) {
          this.$message({
            message: '修改成功',
            type: 'success',
            showClose: true,
          })
          this.getList()
          this.open = false
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm('是否确认删除检查项目为"' + row.item_name + '"的数据项？')
        .then(function () {
          return RemoveItemData(row)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
          this.loading = false
        })
        .catch(() => {})
    },

     async resultData(excelData) {
      console.log("导入", excelData);
       //提交数据库

      await addExcelItem(excelData).then(() => {
        this.$modal.msgSuccess("批量导入信息成功");
        this.getList();
      });
    },
  },
}
</script>

<style lang="scss" scoped>
.el-container {
  max-height: 600px;
}

.qrcodeCon {
  text-align: center;
}

.qrcodeUrl {
  padding: 15px 0;
}

.qrcodeDownload {
  text-align: center;
}
.img {
  width: 100px;
  height: auto;
}
</style>
