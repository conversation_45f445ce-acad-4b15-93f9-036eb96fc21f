/**
 * 验证图片文件
 * @param {File} file - 图片文件
 * @param {Object} options - 验证选项
 * @returns {boolean} 验证结果
 */
export function validateImage(file, options = {}) {
    const {
        maxSize = 2 * 1024 * 1024, // 2MB
        allowedTypes = ['image/jpeg', 'image/png', 'image/gif']
    } = options;

    // 检查文件类型
    if (!allowedTypes.includes(file.type)) {
        throw new Error(`不支持的文件类型，仅支持: ${allowedTypes.join(', ')}`);
    }

    // 检查文件大小
    if (file.size > maxSize) {
        throw new Error(`文件大小超过限制，最大允许: ${(maxSize / 1024 / 1024).toFixed(1)}MB`);
    }

    return true;
}

/**
 * 将文件转换为base64
 * @param {File} file - 图片文件
 * @returns {Promise<string>} base64字符串
 */
export function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
}

/**
 * 简单的图片压缩
 * @param {File} file - 原始图片文件
 * @param {Object} options - 压缩选项
 * @returns {Promise<string>} 压缩后的base64
 */
export function compressImage(file, options = {}) {
    const {
        maxWidth = 800,
        maxHeight = 600,
        quality = 0.8
    } = options;

    return new Promise((resolve, reject) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();

        img.onload = () => {
            // 计算新尺寸
            let { width, height } = img;

            if (width > maxWidth || height > maxHeight) {
                const ratio = Math.min(maxWidth / width, maxHeight / height);
                width *= ratio;
                height *= ratio;
            }

            canvas.width = width;
            canvas.height = height;

            // 绘制图片
            ctx.drawImage(img, 0, 0, width, height);

            // 转换为base64
            const compressedBase64 = canvas.toDataURL('image/jpeg', quality);
            resolve(compressedBase64);
        };

        img.onerror = () => reject(new Error('图片加载失败'));
        img.src = URL.createObjectURL(file);
    });
}

/**
 * 处理图片上传
 * @param {File} file - 图片文件
 * @param {Object} options - 处理选项
 * @returns {Promise<string>} 处理后的base64
 */
export async function handleImageUpload(file, options = {}) {
    const {
        validate = true,
        compress = false,
        maxSize = 2 * 1024 * 1024,
        allowedTypes = ['image/jpeg', 'image/png'],
        ...compressOptions
    } = options;

    try {
        // 验证文件
        if (validate) {
            validateImage(file, { maxSize, allowedTypes });
        }

        // 压缩或直接转换
        if (compress) {
            return await compressImage(file, compressOptions);
        } else {
            return await fileToBase64(file);
        }
    } catch (error) {
        throw new Error(`图片处理失败: ${error.message}`);
    }
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
export function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 获取图片信息
 * @param {File} file - 图片文件
 * @returns {Promise<Object>} 图片信息
 */
export function getImageInfo(file) {
    return new Promise((resolve, reject) => {
        const img = new Image();

        img.onload = () => {
            resolve({
                width: img.width,
                height: img.height,
                size: file.size,
                type: file.type,
                name: file.name,
                sizeText: formatFileSize(file.size)
            });
        };

        img.onerror = () => reject(new Error('无法获取图片信息'));
        img.src = URL.createObjectURL(file);
    });
}

/**
 * 创建图片预览URL
 * @param {File} file - 图片文件
 * @returns {string} 预览URL
 */
export function createPreviewUrl(file) {
    return URL.createObjectURL(file);
}

/**
 * 释放预览URL
 * @param {string} url - 预览URL
 */
export function revokePreviewUrl(url) {
    URL.revokeObjectURL(url);
}

// 默认配置
export const DEFAULT_CONFIG = {
    avatar: {
        maxSize: 2 * 1024 * 1024, // 2MB
        allowedTypes: ['image/jpeg', 'image/png'],
        compress: true,
        maxWidth: 400,
        maxHeight: 400,
        quality: 0.8
    },
    qrcode: {
        maxSize: 2 * 1024 * 1024, // 2MB
        allowedTypes: ['image/jpeg', 'image/png'],
        compress: true,
        maxWidth: 300,
        maxHeight: 300,
        quality: 0.9
    }
};

/**
 * 快速处理头像
 * @param {File} file - 图片文件
 * @returns {Promise<string>} base64字符串
 */
export function handleAvatar(file) {
    return handleImageUpload(file, DEFAULT_CONFIG.avatar);
}

/**
 * 快速处理二维码
 * @param {File} file - 图片文件
 * @returns {Promise<string>} base64字符串
 */
export function handleQrcode(file) {
    return handleImageUpload(file, DEFAULT_CONFIG.qrcode);
}
