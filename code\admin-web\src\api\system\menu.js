import request from "@/utils/request";
const baseURL = "menu/";

// 查询菜单列表
export function listMenu(query) {
  return request({
    url: baseURL + "listMenu",
    method: "get",
    params: query,
  });
}

// 查询菜单详细
export function getMenu(menuId) {
  return request({
    url: baseURL + "getMenu?menuId=" + menuId,
    method: "get",
  });
}

// 查询菜单下拉树结构
export function treeselect() {
  return request({
    url: baseURL + "treeselect",
    method: "get",
  });
}

// 根据角色ID查询菜单下拉树结构
export function roleMenuTreeselect(roleId) {
  return request({
    url: baseURL + "roleMenuTreeselect?roleId=" + roleId,
    method: "get",
  });
}

// 新增菜单
export function addMenu(data) {
  return request({
    url: baseURL + "addMenu",
    method: "post",
    data: data,
  });
}

// 修改菜单
export function updateMenu(data) {
  return request({
    url: baseURL + "updateMenu",
    method: "put",
    data: data,
  });
}

// 删除菜单
export function delMenu(menuId) {
  return request({
    url: baseURL + 'delMenu?menuId=' + menuId,
    method: 'delete'
  })
}
