import request from '@/utils/request'
const baseURL = 'expert/'



export function CommonFileUpload(data) {
    return request({
        url: baseURL + 'CommonFileUpload',
        method: 'post',
        data
    })
}
export function UpdateExperts(data) {
    return request({
        url: baseURL + 'UpdateExperts',
        method: 'post',
        data
    })
}

export function GetExpertsList(data) {
    return request({
        url: baseURL + 'GetExpertsList',
        method: 'get',
        params: data
    })
}
export function ToDelExperts(id) {
    return request({
        url: baseURL + 'ToDelExperts?id=' + id,
        method: 'get'

    })
}
export function ToDetailExperts(id) {
    return request({
        url: baseURL + 'ToDetailExperts?id=' + id,
        method: 'get'

    })
}

export function StatusChange(id) {
    return request({
        url: baseURL + 'StatusChange?id=' + id,
        method: 'get'

    })
}
