import request from '@/utils/request'

export function GetUserList(data) {
  return request({
    url: 'SystemUser/GetUserList',
    method: 'post',
    data: data
  })
}

export function AddUser(data) {
  return request({
    url: 'SystemUser/AddUser',
    method: 'post',
    data: data
  })
}

export function UpdateUser(data) {
  return request({
    url: 'SystemUser/UpdateUser',
    method: 'post',
    data: data
  })
}

export function PassWordReset(id) {
  return request({
    url: 'SystemUser/PassWordReset?id=' + id,
    method: 'put',
  })
}


export function Cancel(id) {
  return request({
    url: 'SystemUser/Cancel?id=' + id,
    method: 'put',
  })
}

export function BlockUp(id) {
  return request({
    url: 'SystemUser/BlockUp?id=' + id,
    method: 'put',
  })
}
