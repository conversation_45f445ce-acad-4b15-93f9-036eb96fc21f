import request from '@/utils/request'
const baseURL = 'aomenu/'

// 查询菜单列表
export function listAoMenu(query) {
  return request({
    url: baseURL + 'listAoMenu',
    method: 'get',
    params: query,
  })
}

// 查询菜单详细
export function getAoMenuById(menuId) {
  return request({
    url: baseURL + 'getAoMenuById?menuId=' + menuId,
    method: 'get',
  })
}

// 新增菜单
export function addAoMenu(data) {
  return request({
    url: baseURL + 'addAoMenu',
    method: 'post',
    data: data,
  })
}

// 修改菜单
export function updateAoMenu(data) {
  return request({
    url: baseURL + 'updateAoMenu',
    method: 'put',
    data: data,
  })
}

// 删除菜单
export function delAoMenu(menuId) {
  return request({
    url: baseURL + 'delAoMenu?menuId=' + menuId,
    method: 'delete',
  })
}
