<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="标题" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入标题名称" clearable style="width: 240px"
                  @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="restQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addButton"
               v-hasPermi="['system:role:add']">新增
    </el-button>

    <!--    列表-->
    <el-table v-loading="loading" :data="pageList" @selection-change="">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="标题" prop="title" align="center" width="250"/>
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.create_time) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="置顶照片" align="center" prop="showPhotoWall" width="150">
        <template slot-scope="scope">
          <el-popover placement="top" title="" trigger="hover">
            <el-image :src="scope.row.img_path" style="max-height: 600px; max-width: 900px;"></el-image>
            <el-image slot="reference" :src="scope.row.img_path" :alt="scope.row.img_path"></el-image>
          </el-popover>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="newsUpdateButton3(scope.row)">标题修改
          </el-button>
          <br/>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="newsUpdateButton2(scope.row)">图片修改
          </el-button>
          <br/>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="newsDelete(scope.row.id,scope.row.title)">
            新闻删除
          </el-button>
          <br/>
        </template>
      </el-table-column>
    </el-table>

    <!--     添加对话框 -->
    <el-dialog :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="forms" :rules="rules" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="forms.title" placeholder="请输入角色名称"/>
        </el-form-item>
        <el-form-item label="置顶图片">
          <el-upload list-type="picture-card" action="#" :multiple="false" :show-file-list="true"
                     :before-upload="beforeUpload" accept=".jpg, .png, .jpeg, .bmp,.webp" :limit="1"
                     :auto-upload="false"
                     :file-list="fileList" :on-remove="deleteFile" :on-change="onChange">
            <i class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4}"
            placeholder="请输入内容"
            v-model="forms.remark">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!--    修改图片-->
    <el-dialog :visible.sync="openUpdate2" width="30%" append-to-body>
      <el-form ref="form" :model="updateForms" :rules="rules" label-width="60px">
        <el-form-item label="置顶图片" label-width="130px">
          <el-upload list-type="picture-card" action="#" :multiple="false" :show-file-list="true"
                     :before-upload="beforeUpload" accept=".jpg, .png, .jpeg, .bmp,.webp" :limit="1"
                     :auto-upload="false"
                     :file-list="fileList" :on-remove="deleteFile" :on-change="onChange2">
            <i class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="newsUpdate">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!--    修改标题-->
    <el-dialog :visible.sync="openUpdate3" width="30%" append-to-body>
      <el-form ref="form" :model="updateForms" :rules="rules" label-width="60px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="updateForms.title" placeholder="请输入角色名称"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="newsUpdate">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!--    分页-->
    <pagination v-show="page > 0" :total="page" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="handleQuery"/>
  </div>
</template>

<script>
import {md5} from "@/utils/md5";
import {UploadFile} from '@/api/news/release'
import {
  SaveBase,
  QueryBase,
  DeleteBase,
  UpdateBase,
} from '@/api/infrastructure/base'
import env from '@/utils/ApiConfig'


export default {
  name: "base",
  data() {
    return {
      baseURL: env.get_base_url(),
      logo: "",
      loading: false,
      open: false,
      openUpdate2: false,
      openUpdate3: false,
      fileList: [],
      showSearch: true,
      queryParams: {
        title: undefined,
        pageNum: 1,
        pageSize: 10,
      },
      forms: {
        showPhotoWall: undefined,
        remark: undefined,
        title: undefined,
        showPhotoWallMd5: undefined,
      },
      updateForms: {},
      pageList: [],
      page: 0,
      rules: {
        title: [
          {required: true, message: "新闻标题不能为空", trigger: "blur"}
        ],
      },
    }
  },
  created() {
    this.logo = this.baseURL + "file/logo.png";
    this.handleQuery();
  },
  methods: {
    submitForm() {
      //获取标签内容
      this.$refs["form"].validate(valid => {
        if (valid) {
          SaveBase(this.forms).then(res => {
            if (res.code === 200) {
              this.$message.success(res.message);
              this.cancel();
              this.handleQuery();
            }
          })
        }
      })

    },
    //修改接口
    newsUpdate() {
      UpdateBase(this.updateForms).then(res => {
        if (res.code === 200) {
          this.$message.success(res.message)
          this.openUpdate2 = false;
          this.openUpdate3 = false;
          this.updateForms = {};
        }
      })
    },
    //图片修改
    newsUpdateButton2(data) {
      this.fileList = [];
      this.updateForms = data;
      this.openUpdate2 = true;
    },
    //标题修改
    newsUpdateButton3(data) {
      this.updateForms = data;
      this.openUpdate3 = true;
    },
    //文本修改
    //新闻删除
    newsDelete(id, title) {
      this.$confirm('您确定要删除《' + title + '》的基础设施信息?,本次操作将遗留操作痕迹, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        DeleteBase(id).then(res => {
          if (res.code === 200) {
            this.$message.success(res.message);
            this.handleQuery();
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });

    },
    //新闻详情
    // 选取完文件触发事件
    onChange(a, fileList) {
      let isLt2M = a.size / 1024 / 1024 < 0.5
      if (!isLt2M){
        this.$message.error('置顶图片大小不能超过0.5MB!')
        this.fileList = [];
        return;
      }
      const loading = this.$loading({
        lock: true,
        text: '休息一下,文件上传中(●'+ '◡' +'●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.fileList = fileList
      const param = new FormData()
      param.append('file', a.raw)
      md5(a.raw).then(res => {
        param.append("md5", res)
        UploadFile(param).then(response => {
          this.forms.showPhotoWall = this.baseURL + response.data.path;
          this.forms.showPhotoWallMd5 = response.data.md5;
          loading.close()
        })
      })
    },
    onChange2(a, fileList) {
      let isLt2M = a.size / 1024 / 1024 < 0.5
      if (!isLt2M){
        this.$message.error('置顶图片大小不能超过0.5MB!')
        this.fileList = [];
        return;
      }
      const loading = this.$loading({
        lock: true,
        text: '休息一下,文件上传中(●'+ '◡' +'●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.fileList = fileList
      const param = new FormData()
      param.append('file', a.raw)
      md5(a.raw).then(res => {
        param.append("md5", res)
        UploadFile(param).then(response => {
          this.updateForms.img_path = this.baseURL + response.data.path;
          this.updateForms.img_md5 = response.data.md5;
          loading.close()
        })
      })
    },
    //照片删除
    deleteFile(a, fileList) {
      // if (this.form.showPhotoWall != undefined) {
      //   delFile(this.form.showPhotoWall);
      // }
    },
    //上传成功后关闭弹窗并调用查询方法刷新页面数据
    beforeUpload(file) {
      // uploadFile(file).then(response => {
      //   this.forms.showPhotoWall = response.data
      // })
    },
    //新增
    addButton() {
      this.fileList = [];
      this.open = true;
      this.restForms();

    },
    //取消
    cancel() {
      this.open = false;
      this.openUpdate = false;
      this.openUpdate2 = false;
      this.openUpdate3 = false;
      this.handleQuery();
    },
    //下一步
    // 数据查询
    handleQuery() {
      QueryBase(this.queryParams).then(res => {
        if (res.code === 200) {
          this.pageList = this.handleD(res.data.list);
          this.page = res.data.page;
          this.$message.success(res.message);
        }
      })
    },
    handleD(data) {
      let datas = [];
      let logos = this.logo;
      data.forEach(function (item, index) {
        if (item.img_path === null || item.img_path === '') {
          item.img_path = logos;
        }
        datas.push(item);
      })
      return datas;
    },
    //数据重置
    restForms() {
      this.forms = {
        showPhotoWall: undefined,
        showPhotoWallMd5: undefined,
        remark: undefined,
        html: undefined,
      }
    },
    restQuery() {
      this.queryParams = {
        title: undefined,
        pageNum: 1,
        pageSize: 10,
      }
    }
  },
}
</script>

<style scoped>
/deep/ .pagination-container {
  position: relative;
  height: 25px;
  margin-bottom: 10px;
  margin-top: 15px;
  padding: 10px 20px !important;
  margin-right: 40%;
}
</style>
<style src="@wangeditor/editor/dist/css/style.css"></style>
