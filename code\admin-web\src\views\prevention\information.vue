<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="标题:" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入标题名称" clearable style="width: 240px"
                  @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="主题:" prop="title">
        <el-select v-model="queryParams.typeTwo" filterable placeholder="请选择">
          <el-option
            v-for="item in selectDict"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态:" prop="title">
        <el-select v-model="queryParams.auditStatus" filterable placeholder="请选择">
          <el-option
            v-for="item in auditStatusList"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="restQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addButton"
               v-hasPermi="['system:role:add']">新增
    </el-button>

    <!--    列表-->
    <el-table v-loading="loading" :data="pageList" @selection-change="">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="标题" prop="title" align="center" width="250"/>
      <el-table-column label="文本内容" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope" sortable>
          <el-button size="mini" type="text" icon="el-icon-document" @click="newsParticulars(scope.row.html)">详情
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="置顶照片" align="center" width="150">
        <template slot-scope="scope">
          <el-popover placement="top" title="" trigger="hover">
            <el-image :src="scope.row.top_img_path" style="max-height: 600px; max-width: 900px;"></el-image>
            <el-image slot="reference" :src="scope.row.top_img_path" :alt="scope.row.top_img_path"></el-image>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.create_time) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.audit_status === '0'"><el-tag>通过</el-tag></span>
          <span v-else-if="scope.row.audit_status === '1'"><el-tag>审核中</el-tag></span>
          <span v-else-if="scope.row.audit_status === '2'"><el-tag type="danger">驳回</el-tag></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="newsUpdateButton3(scope.row)" >标题修改
          </el-button><br/>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="newsUpdateButton2(scope.row)" >图片修改
          </el-button><br/>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="newsUpdateButton1(scope.row)" >文本修改
          </el-button><br/>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="newsDelete(scope.row.id,scope.row.title)">资讯删除
          </el-button><br/>
        </template>
      </el-table-column>
    </el-table>

    <!--     添加对话框 -->
    <el-dialog :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="forms" :rules="rules" label-width="100px">
        <el-form-item label="标题：" prop="title">
          <el-input v-model="forms.title" placeholder="请输入标题名称"/>
        </el-form-item>
        <el-form-item label="主题：" prop="type">
          <el-select v-model="forms.type" filterable placeholder="请选择">
            <el-option
              v-for="item in selectDict"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="置顶图片：">
          <el-upload list-type="picture-card" action="#" :multiple="false" :show-file-list="true"
                     :before-upload="beforeUpload" accept=".jpg, .png, .jpeg, .bmp,.webp" :limit="1"
                     :auto-upload="false"
                     :file-list="fileList" :on-remove="deleteFile" :on-change="onChange">
            <i class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注：">
          <el-input
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4}"
            placeholder="请输入内容"
            v-model="forms.remark">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="nextStep">下一步</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!--    新增文本域-->
    <el-dialog :visible.sync="next" style="margin-top: -1.5%" width="95%" append-to-body>
      <div style="border: 1px solid #ccc;">
        <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig" :mode="mode"/>
        <Editor style="height: 700px; overflow-y: hidden;" v-model="forms.html" :defaultConfig="editorConfig"
                :mode="mode"
                @onCreated="onCreated"/>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="">上一步</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!--    修改文本域-->
    <el-dialog :visible.sync="openUpdate" style="margin-top: -1.5%" width="95%" append-to-body>
      <div style="border: 1px solid #ccc;">
        <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig" :mode="mode"/>
        <Editor style="height: 700px; overflow-y: hidden;" v-model="updateForms.html" :defaultConfig="editorConfig"
                :mode="mode"
                @onCreated="onCreated"/>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="newsUpdate">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!--    修改图片-->
    <el-dialog :visible.sync="openUpdate2" width="30%" append-to-body>
      <el-form ref="form" :model="updateForms" :rules="rules" label-width="60px">
        <el-form-item label="置顶图片" label-width="130px">
          <el-upload list-type="picture-card" action="#" :multiple="false" :show-file-list="true"
                     :before-upload="beforeUpload" accept=".jpg, .png, .jpeg, .bmp,.webp" :limit="1"
                     :auto-upload="false"
                     :file-list="fileList" :on-remove="deleteFile" :on-change="onChange2">
            <i class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="newsUpdate">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!--    修改标题-->
    <el-dialog :visible.sync="openUpdate3" width="30%" append-to-body>
      <el-form ref="form" :model="updateForms" :rules="rules" label-width="80px">
        <el-form-item label="标题：" prop="title">
          <el-input v-model="updateForms.title" placeholder="请输入标题名称"/>
        </el-form-item>
        <el-form-item label="主题：" prop="type" label-width="80px">
          <el-select v-model="updateForms.type" filterable placeholder="请选择">
            <el-option
              v-for="item in selectDict"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="newsUpdate">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!--    分页-->
    <pagination v-show="page > 0" :total="page" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="handleQuery"/>

    <!--     新闻显示-->
    <el-dialog :visible.sync="NewsLook" style="margin-top: -2%" width="80%" append-to-body>
      <div class="new-item">
        <p v-html="textsss"></p>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {md5} from "@/utils/md5";
import {Editor, Toolbar} from '@wangeditor/editor-for-vue'
import {UploadFile} from '@/api/news/release'
import {
  SaveHealthManagements,
  QueryHealthManagementsTypeAsPolicy,
  DeleteHealthManagements,
  UpdateHealthManagements,
  GetDictLabelTree,
} from '@/api/prevention/manage'
import env from '@/utils/ApiConfig'


export default {
  name: "information",
  components: {Editor, Toolbar},
  data() {
    return {
      mode: 'default', // or 'simple'
      html: undefined,
      baseURL: env.get_base_url(),
      logo: "",
      loading: false,
      open: false,
      openUpdate: false,
      openUpdate2: false,
      openUpdate3: false,
      fileList: [],
      toolbarConfig: {},
      editorConfig: {
        MENU_CONF: {
          uploadImage: {
            customUpload: async (file, insertFn) => {
              const loading = this.$loading({
                lock: true,
                text: '休息一下,文件上传中(●'+ '◡' +'●)',
                spinner: 'el-icon-coffee-cup',
                background: 'rgba(0, 0, 0, 0.7)'
              });
              const param = new FormData()
              param.append('file', file)
              md5(file).then(res => {
                param.append("md5", res)
                UploadFile(param).then(response => {
                  insertFn(this.baseURL + response.data.path);
                  loading.close();
                })
              })
            },
          },
          uploadVideo: {
            customUpload: async (file, insertFn) => {
              const loading = this.$loading({
                lock: true,
                text: '休息一下,文件上传中(●'+ '◡' +'●)',
                spinner: 'el-icon-coffee-cup',
                background: 'rgba(0, 0, 0, 0.7)'
              });
              const param = new FormData()
              param.append('file', file)
              md5(file).then(res => {
                param.append("md5", res)
                UploadFile(param).then(response => {
                  insertFn(this.baseURL + response.data.path);
                  loading.close()
                })
              })
            },
          },
        },
      },
      editor: null,
      showSearch: true,
      queryParams: {
        auditStatus: '5',
        title: undefined,
        pageNum: 1,
        pageSize: 10,
      },
      auditStatusList: [
        {
          value: '5',
          label: '全部'
        },{
          value: '0',
          label: '通过'
        },{
          value: '1',
          label: '审核中'
        },{
          value: '2',
          label: '驳回'
        },
      ],
      selectDict:[],
      forms: {
        showPhotoWall: undefined,
        remark: undefined,
        title: undefined,
        html: undefined,
        showPhotoWallMd5: undefined,
        type:''
      },
      updateForms:{},
      pageList: [],
      page: 0,
      next: false,
      textsss: '',
      NewsLook: false,
      rules: {
        title: [
          {required: true, message: "新闻标题不能为空", trigger: "blur"}
        ],
        type:[
          {required: true, message: "新闻主题不能为空", trigger: "blur"}
        ]
      },
    }
  },
  created() {
    this.logo = this.baseURL + "file/logo.png";
    this.handleQuery();
    this.dict();
  },
  methods: {
    insertText() {
      const editor = this.editor // 获取 editor 实例
      if (editor == null) return
      // 调用 editor 属性和 API
      this.forms.html = editor.getHtml()
    },
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
    },
    submitForm() {
      //获取标签内容
      this.insertText();
      SaveHealthManagements(this.forms).then(res => {
        if (res.code === 200) {
          this.$message.success(res.message);
          this.cancel();
          this.handleQuery();
        }
      })
    },
    //修改接口
    newsUpdate(){
      UpdateHealthManagements(this.updateForms).then(res => {
        if (res.code === 200){
          this.$message.success(res.message)
          this.openUpdate = false;
          this.openUpdate2 = false;
          this.openUpdate3 = false;
          this.updateForms = {};
        }
      })
    },
    dict(){
      GetDictLabelTree("lung-cancer-prevention").then(res => {
        this.selectDict = res.data;
      })
    },
    //图片修改
    newsUpdateButton2(data){
      this.fileList = [];
      this.updateForms = data;
      this.openUpdate2 = true;
    },
    //标题修改
    newsUpdateButton3(data){
      this.updateForms = data;
      this.openUpdate3 = true;
    },
    //文本修改
    newsUpdateButton1(data){
      this.updateForms = data;
      this.openUpdate = true;
    },
    //新闻删除
    newsDelete(id,title){
      this.$confirm('您确定要删除《'+ title+'》的新闻信息,本次操作将遗留操作痕迹, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        DeleteHealthManagements(id).then(res => {
          if (res.code === 200) {
            this.$message.success(res.message);
            this.handleQuery();
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });

    },
    //新闻详情
    newsParticulars(text) {
      this.textsss = text;
      this.NewsLook = true;
    },
    // 选取完文件触发事件
    onChange(a, fileList) {
      let isLt2M = a.size / 1024 / 1024 < 0.5
      if (!isLt2M){
        this.$message.error('置顶图片大小不能超过0.5MB!')
        this.fileList = [];
        return;
      }
      const loading = this.$loading({
        lock: true,
        text: '休息一下,文件上传中(●'+ '◡' +'●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.fileList = fileList
      const param = new FormData()
      param.append('file', a.raw)
      md5(a.raw).then(res => {
        param.append("md5", res)
        UploadFile(param).then(response => {
          this.forms.showPhotoWall = this.baseURL + response.data.path;
          this.forms.showPhotoWallMd5 = response.data.md5;
          loading.close()
        })
      })
    },
    onChange2(a, fileList) {
      let isLt2M = a.size / 1024 / 1024 < 0.5
      if (!isLt2M){
        this.$message.error('置顶图片大小不能超过0.5MB!')
        this.fileList = [];
        return;
      }
      const loading = this.$loading({
        lock: true,
        text: '休息一下,文件上传中(●'+ '◡' +'●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.fileList = fileList
      const param = new FormData()
      param.append('file', a.raw)
      md5(a.raw).then(res => {
        param.append("md5", res)
        UploadFile(param).then(response => {
          this.updateForms.top_img_path = this.baseURL + response.data.path;
          this.updateForms.top_img_md5 = response.data.md5;
          loading.close();
        })
      })
    },
    //照片删除
    deleteFile(a, fileList) {
      // if (this.form.showPhotoWall != undefined) {
      //   delFile(this.form.showPhotoWall);
      // }
    },
    //上传成功后关闭弹窗并调用查询方法刷新页面数据
    beforeUpload(file) {
      // uploadFile(file).then(response => {
      //   this.forms.showPhotoWall = response.data
      // })
    },
    //新增
    addButton() {
      this.fileList = [];
      this.open = true;
      this.restForms();

    },
    //取消
    cancel() {
      this.open = false;
      this.next = false;
      this.openUpdate = false;
      this.openUpdate2 = false;
      this.openUpdate3 = false;
      this.handleQuery();
    },
    //下一步
    nextStep() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.next = true;
          this.open = false;
        }
      });


    },
    // 数据查询
    handleQuery() {
      this.pageList = [];
      this.page = 0;
      QueryHealthManagementsTypeAsPolicy(this.queryParams).then(res => {
        if (res.code === 200) {
          this.pageList = this.handleD(res.data.list);
          this.page = res.data.page;
          this.$message.success(res.message);
        }
      })
    },
    handleD(data){
      let datas = [];
      let logos = this.logo;
      data.forEach(function (item,index){
        if (item.top_img_path === null || item.top_img_path === ''){
          item.top_img_path = logos;
        }
        datas.push(item);
      })
      return datas;
    },
    //数据重置
    restForms() {
      this.forms = {
        showPhotoWall: undefined,
        showPhotoWallMd5: undefined,
        remark: undefined,
        title: undefined,
        html: undefined,
        type:''
      }
    },
    restQuery() {
      this.queryParams = {
        auditStatus: '5',
        title: undefined,
        pageNum: 1,
        pageSize: 10,
      }
    }
  },
}
</script>

<style scoped>
/deep/ .pagination-container {
  position: relative;
  height: 25px;
  margin-bottom: 10px;
  margin-top: 15px;
  padding: 10px 20px !important;
  margin-right: 40%;
}
::v-deep.new-item img{
    max-width: 95%;
}
::v-deep.new-item video{
  max-width: 95%;
}
</style>
<style src="@wangeditor/editor/dist/css/style.css"></style>
