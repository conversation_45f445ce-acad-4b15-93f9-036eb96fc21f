<template>
  <div>
    <div class="new-item">
      <p v-html="htmlText"></p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'htmlParticulars',
  props:['htmlText'],
  data() {
    return {
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {}
}
</script>

<style scoped lang="scss">
::v-deep.new-item img{
  max-width: 95%;
}
::v-deep.new-item video{
  max-width: 95%;
}
</style>
