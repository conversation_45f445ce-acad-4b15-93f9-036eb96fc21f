import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

const baseURL = 'user/'

//批量导入用户信息
export function addExcelUsers(data) {
  return request({
    url: baseURL + 'addExcelUsers',
    method: 'post',
    data: data
  })
}


// 查询用户列表
export function listUser(query) {
  return request({
    url: baseURL + 'listUser',
    method: 'get',
    params: query
  })
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: baseURL + 'getUser?userId=' + userId,
    method: 'get'
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: baseURL + 'addUser',
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: baseURL + 'updateUser',
    method: 'put',
    data: data
  })
}

// 删除用户
export function delUser(userIds) {
  return request({
    url: baseURL + 'delUser',
    method: 'delete',
    data: userIds
  })
}

// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password
  }
  return request({
    url: baseURL + 'resetUserPwd',
    method: 'put',
    data: data
  })
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: baseURL + 'changeUserStatus',
    method: 'put',
    data: data
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url:  'system/getUserProfile',
    method: 'get'
  })
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: baseURL + 'updateUserProfile',
    method: 'put',
    data: data
  })
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: baseURL + 'updateUserPwd',
    method: 'put',
    params: data
  })
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: baseURL + 'uploadAvatar',
    method: 'post',
    data: data
  })
}

// 查询授权角色
export function getAuthRole(userId) {
  return request({
    url: baseURL + 'getAuthRole?userId=' + userId,
    method: 'get'
  })
}

// 保存授权角色
export function updateAuthRole(data) {
  return request({
    url: baseURL+'updateAuthRole',
    method: 'post',
    data: data
  })
}

// 查询部门下拉树结构
export function deptTreeSelect() {
  return request({
    url: baseURL + 'treeselect',
    method: 'get'
  })
}
