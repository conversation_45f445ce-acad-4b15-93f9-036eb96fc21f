<template>
  <div class="app-main">
    <!-- 用户积分列表 -->
    <div class="search-box">
      <el-input v-model="queryParam.input" placeholder="输入用户ID/手机号/姓名搜索" clearable @input="getList" />
    </div>
    <el-table :data="users" style="width: 100%" @row-click="handleRowClick" border height="600"
      :header-cell-style="{ background: '#496791', color: '#FFFFFF', fontWeight: 'bold', fontSize: '14px', textAlign: 'center' }"
      :cell-style="{ background: '#496791', color: '#FFFFFF', fontWeight: 'bold', fontSize: '14px', textAlign: 'center' }">
  
      <el-table-column prop="phone" label="手机号" width="150" />
      <el-table-column prop="name" label="姓名" />
      <el-table-column prop="sex" label="性别" />
      <el-table-column prop="idNo" label="身份证号" />
      <el-table-column prop="typeName" label="级别" />
  
      <el-table-column prop="integralTotal" label="当前积分" width="120" />
    </el-table>
    <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="1"
      :page-sizes="[10,50, 100, 500, 1000]" :page-size="10" layout="total, sizes, prev, pager, next, jumper"
      :total="total"></el-pagination>
    <!-- 策略选择弹窗 -->
    <el-dialog title="积分兑换" :visible.sync="dialogVisible" width="80%" custom-class="custom-dialog-style">
      <div class="strategy-container">
        <!-- 策略主表 -->
        <div class="ploy-master">
          <el-tag>点击套餐查看套餐包含的项目</el-tag>
          <el-table :data="ployMasters" @row-click="handlePloySelect" border @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column prop="name" label="名称" />
            <el-table-column prop="points" label="所需积分" />
            <el-table-column prop="type" label="类型" />
          </el-table>
        </div>
  
        <!-- 策略详情表 -->
        <div class="ploy-detail">
          <el-tag>{{selectedPloy.name==undefined?'套餐名称：':'套餐名称：'+selectedPloy.name}}</el-tag>
          <el-table :data="ployDetails" border>
            <el-table-column prop="itemName" label="项目名称" />
            <el-table-column prop="description" label="描述" />
            <el-table-column prop="type" label="类型" />
          </el-table>
        </div>
      </div>
  
      <span slot="footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleExchange">确定兑换</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  GainPloyMaster,
  GainPloyDetail,
  GainUserRecordList,
  ExchangeRecord
} from '@/api/integra/integraCard'
export default {
  data() {
    return {
      total: 0,
      queryParam: {
        input: undefined,
        pageNum: 1,
        pageSize: 10,
      },
      searchKeyword: '',
      dialogVisible: false,
      currentUser: null,
      selectedPloy: [],

      // 假数据
      users: undefined,
      //   [
      //   {
      //     Id: '1001',
      //     phonenumber: '13800138000',
      //     User_name: '张三',
      //     sex: '男',
      //     birthday: '1990-01-01',
      //     address: '北京市海淀区',
      //     idNo: '******************',
      //     integral_total: 6500
      //   },
      //   {
      //     Id: '1002',
      //     phonenumber: '13900139000',
      //     User_name: '李四',
      //     sex: '男',
      //     birthday: '1990-01-01',
      //     address: '北京市海淀区',
      //     idNo: '******************',
      //     integral_total: 2800
      //   }
      // ],

      ployMasters: [

      ],

      ployDetails: [

      ],

      integralExchangeRecords: [],
      multipleSelection: [],
    }
  },

  computed: {

  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      GainUserRecordList(this.queryParam).then(res => {
        this.users = res.data.userList
        this.total = res.data.total
      })
    },
    handleRowClick(row) {
      this.currentUser = row
      console.log('this.currentUser,', this.currentUser);

      this.dialogVisible = true
      GainPloyMaster(row).then(res => {
        this.ployMasters = res.data
      })
    },
    handlePloySelect(selection) {
      this.selectedPloy = selection
      GainPloyDetail(selection).then(res => {
        this.ployDetails = res.data
      })
    },
    handleSelectionChange(selection) {

      this.multipleSelection = selection;
    },
    handleExchange() {
      if (this.multipleSelection.length == 0) {
        this.$message.warning('请先选择策略')
        return
      }
      let param = {
        recordMaster: this.currentUser,
        ployMasters: this.multipleSelection
      }
      ExchangeRecord(param).then((response) => {
        this.$message.success(response.message)

      }).finally(t => {
        this.getList()
        this.dialogVisible = false
      });

    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {

      this.queryParams.pageNum = val;
      this.getList();
    },
  }
}
</script>

<style scoped>
::v-deep .el-pagination__total {
  color: #FFFF;
  /* 修改为你想要的颜色 */
}

::v-deep .custom-dialog-style {
  border-radius: 16px;
  overflow: hidden;
}

::v-deep .custom-dialog-style .el-dialog__header {
  background-color: #496791;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
}

::v-deep .custom-dialog-style .el-dialog__title {
  color: white;
}

.app-main {
  padding: 20px;
  background-color: #496791;
}

.strategy-container {
  display: flex;
  gap: 20px;
}

.ploy-master {
  flex: 1;
}

.ploy-detail {
  flex: 1;
}

.search-box {
  margin-bottom: 20px;
  width: 300px;
}
</style>