<template>
  <div>
    <div>
      <el-table :data="pageList" >
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="标题" prop="title" align="center" width="250"/>
        <el-table-column label="文本内容" align="center" :show-overflow-tooltip="true">
          <template slot-scope="scope" sortable>
            <el-button size="mini" type="text" icon="el-icon-document" @click="newsParticulars(scope.row.html)">详情
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="置顶照片" align="center" width="150">
          <template slot-scope="scope">
            <el-popover placement="top" title="" trigger="hover">
              <el-image :src="scope.row.top_img_path" style="max-height: 600px; max-width: 900px;"></el-image>
              <el-image slot="reference" :src="scope.row.top_img_path" :alt="scope.row.top_img_path"></el-image>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="提交时间" align="center">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.create_time) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="审核状态" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.audit_status === '0'"><el-tag>通过</el-tag></span>
            <span v-else-if="scope.row.audit_status === '1'"><el-tag>审核中</el-tag></span>
            <span v-else-if="scope.row.audit_status === '2'"><el-tag type="danger">驳回</el-tag></span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-circle-check" @click="auditInformation(scope.row.id,'0')" >审核
            </el-button><br/>
            <el-button size="mini" type="text" icon="el-icon-circle-close" @click="auditInformation(scope.row.id,'1')">驳回
            </el-button><br/>
          </template>
        </el-table-column>
      </el-table>
    </div>


    <div>
      <el-dialog :visible.sync="NewsLook" style="margin-top: -2%" width="80%" append-to-body>
        <div class="new-item">
          <p v-html="textsss"></p>
        </div>
      </el-dialog>
    </div>

  </div>
</template>

<script>
import {
  GetPreventionManagementAuditPageList,
  AuditInformation,} from '@/api/prevention/admin'
import env from '@/utils/ApiConfig'

export default {
  name: 'audit',
  data() {
    return {
      queueFrom: {
        pageNum: 1,
        pageSize: 10
      },
      logo: "",
      baseURL: env.get_base_url(),
      pageList:[],
      page: 0,
      NewsLook: false,
      textsss: '',
    }
  },
  created() {
    this.logo = this.baseURL + "file/logo.png";
    this.getPageList();
  },
  mounted() {
  },
  methods: {
    //信息审核
    auditInformation(id,type){
      AuditInformation(id,type).then(res => {
        this.$message.success(res.message)
        this.getPageList();
      })
    },
    //获取分页信息
    getPageList(){
      this.pageList = [];
      this.page = 0;
      GetPreventionManagementAuditPageList(this.queueFrom).then(res => {
        this.pageList = this.topImageDispose(res.data.list);
        this.page = res.data.page;
      })
    },
    //新闻详情
    newsParticulars(text) {
      this.textsss = text;
      this.NewsLook = true;
    },
    //图片处理
    topImageDispose(data){
      let datas = [];
      let logos = this.logo;
      data.forEach(function (item,index){
        if (item.top_img_path === null || item.top_img_path === ''){
          item.top_img_path = logos;
        }
        datas.push(item);
      })
      return datas;
    },
  }
}
</script>

<style scoped lang="scss">
.new-item{
  ::v-deep img{
    max-width: 95%;
  }
  ::v-deep video{
    max-width: 95%;
  }
}
</style>
