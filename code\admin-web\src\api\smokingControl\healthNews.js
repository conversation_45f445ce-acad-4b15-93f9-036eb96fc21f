import request from '@/utils/request'

export function QuerySmokingControlPageList(param) {
  return request({
    url: 'SmokingControlQueen/QuerySmokingControlPageList',
    method: 'get',
    params: param
  })
}

export function SaveSmokingControl(data) {
  return request({
    url: 'SmokingControlQueen/SaveSmokingControl',
    method: 'post',
    data: data
  })
}

export function UpdateSmokingControl(param) {
  return request({
    url: 'SmokingControlQueen/UpdateSmokingControl',
    method: 'post',
    data: param
  })
}

export function DeleteSmokingControlById(id) {
  return request({
    url: 'SmokingControlQueen/DeleteSmokingControlById?id='+id,
    method: 'post',
  })
}
