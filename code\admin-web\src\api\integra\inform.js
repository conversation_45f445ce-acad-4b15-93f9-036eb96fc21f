import request from '@/utils/request'

export function GetInformPageList(data) {
  return request({
    url: 'IntegralInform/GetInformPageList',
    method: 'post',
    data: data
  })
}

export function AddInform(data) {
  return request({
    url: 'IntegralInform/AddInform',
    method: 'post',
    data: data
  })
}

export function UpdateInform(data) {
  return request({
    url: 'IntegralInform/UpdateInform',
    method: 'put',
    data: data
  })
}

export function BlockUpInform(id) {
  return request({
    url: 'IntegralInform/BlockUpInform?id=' + id,
    method: 'put',
  })
}

export function CancelInform(id) {
  return request({
    url: 'IntegralInform/CancelInform?id=' + id,
    method: 'put',
  })
}
