import request from '@/utils/request'
const baseURL = 'PackageList/'

export function InsertProject(data) {
  return request({
    url: baseURL + 'InsertProject',
    method: 'post',
    data,
  })
}
export function RemoveProject(data) {
  return request({
    url: baseURL + 'RemoveProject',
    method: 'post',
    data,
  })
}
export function SelectProject(data) {
  return request({
    url: baseURL + 'SelectProject',
    method: 'post',
    data,
  })
}
export function InsertPackage(data) {
  return request({
    url: baseURL + 'InsertPackage',
    method: 'post',
    data,
  })
}
export function RemovePackage(data) {
  return request({
    url: baseURL + 'RemovePackage',
    method: 'post',
    data,
  })
}
export function SelectPackage(data) {
  return request({
    url: baseURL + 'SelectPackage',
    method: 'post',
    data,
  })
}