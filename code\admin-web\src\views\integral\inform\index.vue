<template>
  <div class="single-master">
    <div class="single-title">通知/公告模板维护</div>
    <div class="single-element">
      <div class="element-form">
        <el-form :inline="true" :model="queueForm" style="margin-left: 10px;">
          <el-form-item label="内容:">
            <el-input v-model="queueForm.text" placeholder="支持模糊搜索"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getPageList">搜索</el-button>
            <el-button type="danger" @click="resert">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-button type="primary" style="width: 120px;margin-bottom: 2px" @click="addInform">新增模板</el-button>
      <div class="element-table">
        <el-table :data="tableData" border :height="tableHeight" stripe style="width: 100%">
          <el-table-column type="index" align="center" width="50"></el-table-column>
          <el-table-column label="模板内容" prop="text" align="center"/>
          <el-table-column label="状态" prop="iD_No" width="100" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.status === '0'">正常</el-tag>
              <el-tag v-else type="danger">停用</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="create_time" align="center" width="160"/>
          <el-table-column label="操作" align="center" width="160">
            <template slot-scope="scope">
              <el-popover
                placement="top"
                trigger="click"
              >
                <div class="table-button">
                  <el-button type="primary" icon="el-icon-user-solid" style="width: 120px;" @click="updateClick(scope.row)">模板修改</el-button>
                  <el-button type="info" v-if="scope.row.status === '0'" icon="el-icon-close" style="width: 120px;"
                             @click="blockUp(scope.row.id)"
                  >停用
                  </el-button>
                  <el-button type="info" v-if="scope.row.status !== '0'" icon="el-icon-refresh-left"
                             style="width: 120px;" @click="blockUp(scope.row.id)"
                  >恢复
                  </el-button>
                  <el-button type="danger" icon="el-icon-error" style="width: 120px;" @click="cancelInform(scope.row.id)">
                    删除
                  </el-button>
                </div>
                <el-button slot="reference" type="text" icon="el-icon-s-tools">操作</el-button>
              </el-popover>

            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="page">
        <pagination v-show="total > 0" :total="total" :page.sync="queueForm.pageNum" :limit.sync="queueForm.pageSize" @pagination="getPageList"/>
      </div>
    </div>
    <div class="element-drawer">
      <el-dialog
        :title="title"
        :visible.sync="status"
        width="40%"
      >
        <el-form :model="addForm" :rules="rules" ref="ruleForm" label-width="110px">
          <el-form-item label="通知/公告：" prop="text">
            <el-input
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 10}"
              placeholder="请输入内容"
              v-model="addForm.text">
            </el-input>
          </el-form-item>
          <el-form-item>
            <div>
              <el-button type="primary" v-if="!addForm.id" @click="submitOn('ruleForm')">新增</el-button>
              <el-button type="primary" v-if="addForm.id" @click="submitOn('ruleForm')">修改/保存</el-button>
              <el-button type="danger" @click="status = false">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {GetInformPageList,
  AddInform,UpdateInform,BlockUpInform,CancelInform} from "@/api/integra/inform"
export default {
  name: 'index',
  props: [],
  components: {},
  data() {
    return {
      queueForm: {
        pageSize: 10,
        pageNum: 1,
        text: '',
      },
      total: 0,
      tableHeight: '',
      tableData: [],
      status: false,
      title: '新增模板',
      addForm:{
        text: '',
      },
      rules: {
        text: [
          { required: true, message: '请输入用户姓名', trigger: 'blur' }
        ],
      }
    }
  },
  created() {
    this.getPageList()
    this.handleResize()
  },
  mounted() {
    window.addEventListener('resize', this.handleResize) // 添加监听器
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize) // 移除监听器
  },
  methods: {
    submitOn(formName){
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let addForm = this.addForm
          if (addForm.id) {
            //修改
            UpdateInform(addForm).then(res => {
              if (res.code === 200) {
                this.$message.success(res.message)
                this.status = false
                this.getPageList()
              }
            })
          } else {
            AddInform(addForm).then(res => {
              if (res.code === 200) {
                this.$message.success(res.message)
                this.status = false
                this.getPageList()
              }
            })
          }
        }
      })
    },
    cancelInform(id){
      CancelInform(id).then(res => {
        this.$message.success(res.message)
        this.getPageList()
      })
    },
    blockUp(id){
      BlockUpInform(id).then(res => {
        this.$message.success(res.message)
        this.getPageList()
      })
    },
    updateClick(data){
      this.addForm = data;
      this.title = "修改模板";
      this.status = true
    },
    addInform(){
      this.addForm = {
        text: ''
      }
      this.title = "新增模板";
      this.status = true
    },
    getPageList(){
      this.tableData = []
      GetInformPageList(this.queueForm).then(res => {
        if (res.code === 200) {
          this.tableData = res.data.list
          this.total = res.data.total
        }
      })
    },
    resert() {
      this.queueForm = {
        pageSize: 10,
        pageNum: 1,
        text: '',
      }
    },
    handleResize() {
      this.tableHeight = window.innerHeight - 280 // 更新高度数据
    }
  }
}
</script>

<style scoped lang="scss">
@import "@/assets/styles/singlePage";

.table-button {
  display: flex;
  flex-direction: column;

  ::v-deep.el-button + .el-button {
    margin-left: 0px;
    margin-top: 1px;
  }
}
.page{
  ::v-deep.pagination-container {
    position: relative;
    height: 40px;
    margin-bottom: 10px;
    margin-top: 10px;
    padding: 10px 20px !important;
  }
}
</style>
