import request from '@/utils/request'

export function SaveBase(param) {
  return request({
    url: 'InfrastructureBaseQueen/SaveBase',
    method: 'post',
    data: param
  })
}

export function QueryBase(param) {
  return request({
    url: 'InfrastructureBaseQueen/QueryBase',
    method: 'get',
    params: param
  })
}

export function DeleteBase(id) {
  return request({
    url: 'InfrastructureBaseQueen/DeleteBase?id='+id,
    method: 'post',
  })
}

export function UpdateBase(param) {
  return request({
    url: 'InfrastructureBaseQueen/UpdateBase',
    method: 'post',
    data: param
  })
}
