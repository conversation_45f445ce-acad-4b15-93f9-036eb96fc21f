<template>
    <!-- 今日助理维护 -->
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-width="68px">
            <el-form-item label="助理姓名" prop="name">
                <el-input v-model="queryParams.name" placeholder="请输入助理姓名" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="手机号" prop="phone">
                <el-input v-model="queryParams.phone" placeholder="请输入手机号" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                    v-hasPermi="['assistant:add']">新增</el-button>
            </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="assistantList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="序号" type="index" width="50" align="center" />
            <el-table-column label="助理姓名" align="center" prop="name" />
            <el-table-column label="手机号" align="center" prop="phone" />
            <el-table-column label="头像" align="center" prop="photoA" width="100">
                <template slot-scope="scope">
                    <div v-if="scope.row.photoA" class="image-container">
                        <img :src="formatImageSrc(scope.row.photoA)" class="table-image"
                            @click="previewImage(scope.row.photoA)" @error="handleImageError" />
                    </div>
                    <span v-else class="no-image-text">暂无头像</span>
                </template>
            </el-table-column>
            <el-table-column label="微信二维码" align="center" prop="photoB" width="120">
                <template slot-scope="scope">
                    <div v-if="scope.row.photoB" class="image-container">
                        <img :src="formatImageSrc(scope.row.photoB)" class="table-image"
                            @click="previewImage(scope.row.photoB)" @error="handleImageError" />
                    </div>
                    <span v-else class="no-image-text">暂无二维码</span>
                </template>
            </el-table-column>
            <el-table-column label="备注" align="center" prop="content" :show-overflow-tooltip="true" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['assistant:edit']">修改</el-button>
                    <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['assistant:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改助理对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="85px">
                <el-form-item label="助理姓名" prop="name">
                    <el-input v-model="form.name" placeholder="请输入助理姓名" />
                </el-form-item>
                <el-form-item label="手机号" prop="phone">
                    <el-input v-model="form.phone" placeholder="请输入手机号" />
                </el-form-item>
                <el-form-item label="助理头像">
                    <div class="upload-container">
                        <el-upload class="avatar-uploader" action="#" :show-file-list="false"
                            :before-upload="beforeAvatarUpload" :http-request="handleAvatarUpload">
                            <img v-if="form.photoA" :src="form.photoA" class="avatar">
                            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                        </el-upload>
                        <el-button v-if="form.photoA" type="danger" size="mini" icon="el-icon-delete" class="delete-btn"
                            @click="deleteAvatar">
                            删除头像
                        </el-button>
                    </div>
                </el-form-item>
                <el-form-item label="微信二维码">
                    <div class="upload-container">
                        <el-upload class="qrcode-uploader" action="#" :show-file-list="false"
                            :before-upload="beforeQrcodeUpload" :http-request="handleQrcodeUpload">
                            <img v-if="form.photoB" :src="form.photoB" class="qrcode">
                            <i v-else class="el-icon-plus qrcode-uploader-icon"></i>
                        </el-upload>
                        <el-button v-if="form.photoB" type="danger" size="mini" icon="el-icon-delete" class="delete-btn"
                            @click="deleteQrcode">
                            删除二维码
                        </el-button>
                    </div>
                </el-form-item>
                <el-form-item label="备注">
                    <el-input v-model="form.content" type="textarea" placeholder="请输入备注" />
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>

        <!-- 图片预览对话框 -->
        <el-dialog title="图片预览" :visible.sync="previewVisible" width="600px" append-to-body center>
            <div class="preview-container">
                <img :src="previewImageUrl" class="preview-image" />
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    ListAssistant, AddAssistant, GetAssistant, UpdateAssistant, DelAssistant
} from "@/api/assistant/index";
import { handleAvatar, handleQrcode } from "@/utils/simpleImageUtils";

export default {
    name: "Assistant",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 助理表格数据
            assistantList: [],
            // 总条数
            total: 0,
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                name: null,
                phone: null,
                pageNum: 1,
                pageSize: 10
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                name: [
                    { required: true, message: "助理姓名不能为空", trigger: "blur" }
                ],
                phone: [
                    { required: true, message: "手机号不能为空", trigger: "blur" },
                    {
                        pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
                        message: "请输入正确的手机号码",
                        trigger: "blur"
                    }
                ]
            },
            // 图片预览相关
            previewVisible: false,
            previewImageUrl: ''
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询助理列表 */
        getList() {
            this.loading = true;
            ListAssistant(this.queryParams).then(response => {
                this.assistantList = response.data.items;
                this.total = response.data.totalCount;
                this.loading = false;
            }).catch(() => {
                this.loading = false;
                this.$modal.msgError('获取助理列表失败');
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                assistantId: null,
                name: null,
                phone: null,
                photoA: null,
                photoB: null,
                content: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.assistantId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加助理";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids
            GetAssistant(id).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改助理";
            }).catch(() => {
                this.$modal.msgError('获取助理详情失败');
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    const params = {
                        name: this.form.name,
                        phone: this.form.phone,
                        photoA: this.form.photoA,
                        photoB: this.form.photoB,
                        content: this.form.content
                    };
                    if (this.form.id != null) {
                        params.id = this.form.id;
                        UpdateAssistant(params).then(() => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        }).catch(() => {
                            this.$modal.msgError('修改失败');
                        });
                    } else {
                        AddAssistant(params).then(() => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        }).catch(() => {
                            this.$modal.msgError('新增失败');
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const id = row.id || this.ids;
            this.$modal.confirm('是否确认删除助理编号为"' + id + '"的数据项？').then(() => {
                return DelAssistant(id);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch((error) => {
                if (error !== 'cancel') {
                    this.$modal.msgError('删除失败');
                }
            });
        },

        /** 头像上传前的校验 */
        beforeAvatarUpload(file) {
            const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
            const isLt2M = file.size / 1024 / 1024 < 2;
            if (!isJPG) {
                this.$modal.msgError('上传头像图片只能是 JPG/PNG 格式!');
                return false;
            }
            if (!isLt2M) {
                this.$modal.msgError('上传头像图片大小不能超过 2MB!');
                return false;
            }
            return true;
        },

        /** 头像上传处理 */
        async handleAvatarUpload(options) {
            try {
                // 使用简化的头像处理
                const base64 = await handleAvatar(options.file);
                this.form.photoA = base64;
                this.$modal.msgSuccess('头像上传成功');
            } catch (error) {
                this.$modal.msgError(error.message);
            }
        },

        /** 二维码上传前的校验 */
        beforeQrcodeUpload(file) {
            const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
            const isLt2M = file.size / 1024 / 1024 < 2;

            if (!isJPG) {
                this.$modal.msgError('上传二维码图片只能是 JPG/PNG 格式!');
                return false;
            }
            if (!isLt2M) {
                this.$modal.msgError('上传二维码图片大小不能超过 2MB!');
                return false;
            }
            return true;
        },

        /** 二维码上传处理 */
        async handleQrcodeUpload(options) {
            try {
                // 使用简化的二维码处理
                const base64 = await handleQrcode(options.file);
                this.form.photoB = base64;
                this.$modal.msgSuccess('二维码上传成功');
            } catch (error) {
                this.$modal.msgError(error.message);
            }
        },

        /** 删除头像 */
        deleteAvatar() {
            this.$modal.confirm('确认删除当前头像吗？').then(() => {
                this.form.photoA = null;
                this.$modal.msgSuccess('头像删除成功');
            });
        },

        /** 删除二维码 */
        deleteQrcode() {
            this.$modal.confirm('确认删除当前二维码吗？').then(() => {
                this.form.photoB = null;
                this.$modal.msgSuccess('二维码删除成功');
            });
        },

        /** 格式化图片源地址 */
        formatImageSrc(src) {
            if (!src) return '';
            if (src.startsWith('data:image/') || src.startsWith('http://') || src.startsWith('https://')) {
                return src;
            }
            return process.env.VUE_APP_BASE_API + src;
        },

        /** 预览图片 */
        previewImage(src) {
            this.previewImageUrl = this.formatImageSrc(src);
            this.previewVisible = true;
        },

        /** 图片加载错误处理 */
        handleImageError(event) {
            event.target.style.display = 'none';
        }
    }
};
</script>

<style scoped>
/* 上传容器样式 */
.upload-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
}

/* 头像上传样式 */
.avatar-uploader {
    position: relative;
    display: inline-block;
}

.avatar-uploader .el-upload {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    background-color: #fafafa;
}

.avatar-uploader .el-upload:hover {
    border-color: #409EFF;
    background-color: #f0f9ff;
}

.avatar-uploader-icon {
    font-size: 32px;
    color: #8c939d;
    width: 140px;
    height: 140px;
    line-height: 140px;
    text-align: center;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 8px;
}

.avatar-uploader-icon:hover {
    color: #409EFF;
    transform: scale(1.02);
}

.avatar {
    width: 140px;
    height: 140px;
    display: block;
    border-radius: 8px;
    object-fit: cover;
    border: 2px solid #e4e7ed;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.avatar:hover {
    transform: scale(1.02);
    box-shadow: 0 6px 20px rgba(64, 158, 255, 0.3);
    border-color: #409EFF;
}



/* 二维码上传样式 */
.qrcode-uploader {
    position: relative;
    display: inline-block;
}

.qrcode-uploader .el-upload {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    background-color: #fafafa;
}

.qrcode-uploader .el-upload:hover {
    border-color: #67C23A;
    background-color: #f0f9ff;
}

.qrcode-uploader-icon {
    font-size: 32px;
    color: #8c939d;
    width: 140px;
    height: 140px;
    line-height: 140px;
    text-align: center;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 8px;
}

.qrcode-uploader-icon:hover {
    color: #67C23A;
    transform: scale(1.02);
}

.qrcode {
    width: 140px;
    height: 140px;
    display: block;
    border-radius: 8px;
    object-fit: cover;
    border: 2px solid #e4e7ed;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.qrcode:hover {
    transform: scale(1.02);
    box-shadow: 0 6px 20px rgba(103, 194, 58, 0.3);
    border-color: #67C23A;
}



/* 删除按钮样式 */
.delete-btn {
    margin-top: 8px;
    border-radius: 4px;
    font-size: 12px;
    padding: 5px 10px;
    transition: all 0.3s ease;
}

.delete-btn:hover {
    background-color: #f56c6c;
    border-color: #f56c6c;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
}

/* 表格中图片预览样式优化 */
.el-table .cell {
    display: flex;
    align-items: center;
    justify-content: center;
}



/* 表格中图片容器样式 */
.image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 60px;
}

/* 表格中图片样式 */
.table-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-image:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-color: #409EFF;
}

/* 暂无图片的提示样式 */
.no-image-text {
    color: #909399;
    font-size: 12px;
    display: inline-block;
    padding: 4px 8px;
    background-color: #f5f7fa;
    border-radius: 4px;
    border: 1px dashed #dcdfe6;
}

/* 图片预览对话框样式 */
.preview-container {
    text-align: center;
    padding: 20px;
}

.preview-image {
    max-width: 100%;
    max-height: 500px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>