import request from '@/utils/request'

export function GainPloyMaster(param) {
  return request({
    url: 'IntegraCard/GainPloyMaster',
    method: 'post',
    data: param
  })
}
export function GainPloyDetail(param) {
  return request({
    url: 'IntegraCard/GainPloyDetail',
    method: 'post',
    data: param
  })
}
export function RemovePloyMaster(param) {
  return request({
    url: 'IntegraCard/RemovePloyMaster',
    method: 'post',
    data: param
  })
}
export function RemovePloyDetail(param) {
  return request({
    url: 'IntegraCard/RemovePloyDetail',
    method: 'post',
    data: param
  })
}
export function SetPloyMaster(param) {
  return request({
    url: 'IntegraCard/SetPloyMaster',
    method: 'post',
    data: param
  })
}
export function SetPloyDetail(param) {
  return request({
    url: 'IntegraCard/SetPloyDetail',
    method: 'post',
    data: param
  })
}

export function GainUserRecordList(param) {
  return request({
    url: 'IntegraCard/GainUserRecordList',
    method: 'post',
    data: param
  })
}


export function ExchangeRecord(param) {
  return request({
    url: 'IntegraCard/ExchangeRecord',
    method: 'post',
    data: param
  })
}

