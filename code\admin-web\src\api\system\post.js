import request from "@/utils/request";
const baseURL = "post/";

// 查询岗位列表
export function listPost(query) {
  return request({
    url: baseURL + "listPost",
    method: "get",
    params: query,
  });
}

// 查询岗位详细
export function getPost(postId) {
  return request({
    url: baseURL + "getPost?postId=" + postId,
    method: "get",
  });
}

// 新增岗位
export function addPost(data) {
  return request({
    url: baseURL + "addPost",
    method: "post",
    data: data,
  });
}

// 修改岗位
export function updatePost(data) {
  return request({
    url: baseURL + "updatePost",
    method: "put",
    data: data,
  });
}

// 删除岗位
export function delPost(postIds) {
  return request({
    url: baseURL + "delPost",
    method: "delete",
    data: postIds,
  });
}
