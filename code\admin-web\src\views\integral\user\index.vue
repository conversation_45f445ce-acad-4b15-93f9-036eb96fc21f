<template>
  <div class="single-master">
    <div class="single-title">用户管理</div>
    <div class="single-element">
      <div class="element-form">
        <el-form :inline="true" :model="queueForm" style="margin-left: 10px;">
          <el-form-item label="用户名:">
            <el-input v-model="queueForm.userName" placeholder="支持模糊搜索"></el-input>
          </el-form-item>
          <el-form-item label="手机号:">
            <el-input v-model="queueForm.phone" placeholder="支持模糊搜索"></el-input>
          </el-form-item>
          <el-form-item label="身份证号:">
            <el-input v-model="queueForm.idNo" placeholder="支持模糊搜索"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getPageList">搜索</el-button>
            <el-button type="danger" @click="resert">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-button type="primary" style="width: 120px;margin-bottom: 2px" @click="addClick">新增用户</el-button>
      <div class="element-table">
        <el-table :data="tableData" border :height="tableHeight" stripe style="width: 100%">
          <el-table-column type="index" align="center" width="50"></el-table-column>
          <el-table-column label="用户名" prop="user_name" align="center"/>
          <el-table-column label="性别" prop="sex" align="center" width="100"/>
          <el-table-column label="手机号" prop="phonenumber" align="center"/>
          <el-table-column label="身份证号" prop="iD_No" align="center"/>
          <el-table-column label="状态" prop="iD_No" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.status === '0'">正常</el-tag>
              <el-tag v-else type="danger">停用</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="160">
            <template slot-scope="scope">
              <el-popover
                placement="top"
                trigger="click"
              >
                <div class="table-button">
                  <el-button type="primary" icon="el-icon-user-solid" style="width: 120px;" @click="updateClick(scope.row)">信息修改</el-button>
                  <el-button type="warning" icon="el-icon-refresh" style="width: 120px;"
                             @click="passWordReset(scope.row.id)"
                  >密码重置
                  </el-button>
                  <el-button type="info" v-if="scope.row.status === '0'" icon="el-icon-close" style="width: 120px;"
                             @click="blockUp(scope.row.id)"
                  >停用
                  </el-button>
                  <el-button type="info" v-if="scope.row.status !== '0'" icon="el-icon-refresh-left"
                             style="width: 120px;" @click="blockUp(scope.row.id)"
                  >恢复
                  </el-button>
                  <el-button type="danger" icon="el-icon-error" style="width: 120px;" @click="cancelUser(scope.row.id)">
                    注销
                  </el-button>
                </div>
                <el-button slot="reference" type="text" icon="el-icon-s-tools">操作</el-button>
              </el-popover>

            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="page">
        <pagination v-show="total > 0" :total="total" :page.sync="queueForm.pageNum" :limit.sync="queueForm.pageSize" @pagination="getPageList"/>
      </div>
    </div>
    <div class="element-drawer">
      <el-dialog
        :title="title"
        :visible.sync="status"
        width="30%"
      >
        <el-alert title="新创建的用户登录密码默认为身份证号后6位" type="info" show-icon></el-alert>
        <el-form :model="addForm" :rules="rules" ref="ruleForm" label-width="80px">
          <el-form-item label="用户名" prop="name">
            <el-input style="width: 180px;" v-model="addForm.name" placeholder="请输入用户名"></el-input>
          </el-form-item>
          <el-form-item label="性别" prop="sex">
            <el-radio-group v-model="addForm.sex">
              <el-radio :label="'男'">男</el-radio>
              <el-radio :label="'女'">女</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="身份证号" prop="idNo">
            <el-input style="width: 260px;" v-model="addForm.idNo" placeholder="请输入身份证号码"></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="phone">
            <el-input style="width: 180px;" v-model="addForm.phone" placeholder="请输入手机号"></el-input>
          </el-form-item>
          <el-form-item>
            <div>
              <el-button type="primary" v-if="!addForm.id" @click="submitOn('ruleForm')">新增</el-button>
              <el-button type="primary" v-if="addForm.id" @click="submitOn('ruleForm')">修改/保存</el-button>
              <el-button type="danger" @click="status = false">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { GetUserList, PassWordReset, BlockUp, Cancel, AddUser,UpdateUser } from '@/api/integra/user'

export default {
  name: 'index',
  props: [],
  components: {},
  data() {
    return {
      queueForm: {
        pageSize: 10,
        pageNum: 1,
        userName: '',
        phone: '',
        idNo: ''
      },
      total: 0,
      tableHeight: '',
      tableData: [],
      status: false,
      title: '新增用户',
      addForm: {
        id: '',
        name: '',
        sex: '男',
        password: '',
        phone: '',
        idNo: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入用户姓名', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' }
        ],
        sex: [
          { required: true, message: '请选择性别', trigger: 'blur' }
        ],
        idNo: [
          { required: true, message: '请输入身份证号', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getPageList()
    this.handleResize()
  },
  mounted() {
    window.addEventListener('resize', this.handleResize) // 添加监听器
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize) // 移除监听器
  },
  methods: {
    submitOn(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let addForm = this.addForm
          if (addForm.id) {
            //修改
            UpdateUser(addForm).then(res => {
              if (res.code === 200) {
                this.$message.success(res.message)
                this.status = false
                this.getPageList()
              }
            })
          } else {
            AddUser(addForm).then(res => {
              if (res.code === 200) {
                this.$message.success(res.message)
                this.status = false
                this.getPageList()
              }
            })
          }
        }
      })

    },
    addClick() {
      this.addForm = {
        id: '',
        name: '',
        sex: '男',
        password: '',
        phone: '',
        idNo: ''
      }
      this.title = "新增用户";
      this.status = true
    },
    updateClick(row){
      this.addForm = {
        id: row.id,
        name: row.user_name,
        sex: row.sex,
        password: '',
        phone: row.phonenumber,
        idNo: row.iD_No
      }
      this.title = "修改用户"
      this.status = true;
    },
    cancelUser(id) {
      Cancel(id).then(res => {
        this.$message.success(res.message)
        this.getPageList()
      })
    },
    blockUp(id) {
      BlockUp(id).then(res => {
        this.$message.success(res.message)
        this.getPageList()
      })
    },
    passWordReset(id) {
      PassWordReset(id).then(res => {
        this.$message.success(res.message)
        this.getPageList()
      })
    },
    getPageList() {
      this.tableData = []
      GetUserList(this.queueForm).then(res => {
        if (res.code === 200) {
          this.tableData = res.data.list
          this.total = res.data.total
        }
      })
    },
    resert() {
      this.queueForm = {
        pageSize: 10,
        pageNum: 1,
        userName: '',
        phone: '',
        idNo: ''
      }
    },
    handleResize() {
      this.tableHeight = window.innerHeight - 280 // 更新高度数据
    }
  }
}
</script>

<style scoped lang="scss">
@import "@/assets/styles/singlePage";

.table-button {
  display: flex;
  flex-direction: column;

  ::v-deep.el-button + .el-button {
    margin-left: 0px;
    margin-top: 1px;
  }
}
.page{
  ::v-deep.pagination-container {
    position: relative;
    height: 40px;
    margin-bottom: 10px;
    margin-top: 10px;
    padding: 10px 20px !important;
  }
}
</style>
