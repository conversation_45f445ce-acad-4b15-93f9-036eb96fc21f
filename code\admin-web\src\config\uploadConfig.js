/**
 * 图片上传配置
 * 统一管理图片上传的各种策略和参数
 */

// 上传策略枚举
export const UPLOAD_STRATEGY = {
    FILE_UPLOAD: 'file_upload',        // 文件上传到服务器
    COMPRESSED_BASE64: 'compressed_base64', // 压缩后的base64
    ORIGINAL_BASE64: 'original_base64'  // 原始base64（不推荐）
};

// 默认配置
export const DEFAULT_CONFIG = {
    // 当前使用的策略
    strategy: UPLOAD_STRATEGY.FILE_UPLOAD,
    
    // 文件验证规则
    validation: {
        allowedTypes: ['image/jpeg', 'image/png', 'image/gif'],
        maxSize: 2 * 1024 * 1024, // 2MB
        maxWidth: 2000,
        maxHeight: 2000
    },
    
    // 压缩配置
    compression: {
        // 智能压缩目标大小（KB）
        targetSizeKB: 200,
        
        // 手动压缩参数
        manual: {
            maxWidth: 800,
            maxHeight: 600,
            quality: 0.8,
            outputFormat: 'image/jpeg'
        },
        
        // 根据文件大小的自适应压缩策略
        adaptive: {
            // 大于5MB的文件
            large: {
                threshold: 5 * 1024 * 1024,
                maxWidth: 800,
                quality: 0.6
            },
            // 大于2MB的文件
            medium: {
                threshold: 2 * 1024 * 1024,
                maxWidth: 1000,
                quality: 0.7
            },
            // 大于1MB的文件
            small: {
                threshold: 1 * 1024 * 1024,
                maxWidth: 1200,
                quality: 0.8
            },
            // 默认设置
            default: {
                maxWidth: 1200,
                quality: 0.9
            }
        }
    }
};

// 头像专用配置
export const AVATAR_CONFIG = {
    ...DEFAULT_CONFIG,
    validation: {
        allowedTypes: ['image/jpeg', 'image/png'],
        maxSize: 5 * 1024 * 1024, // 5MB，增加文件大小限制
        maxWidth: 2000,  // 增加宽度限制
        maxHeight: 2000  // 增加高度限制
    },
    compression: {
        ...DEFAULT_CONFIG.compression,
        targetSizeKB: 150, // 头像目标更小
        manual: {
            maxWidth: 400,
            maxHeight: 400,
            quality: 0.8,
            outputFormat: 'image/jpeg'
        }
    }
};

// 二维码专用配置
export const QRCODE_CONFIG = {
    ...DEFAULT_CONFIG,
    validation: {
        allowedTypes: ['image/jpeg', 'image/png'],
        maxSize: 5 * 1024 * 1024, // 5MB，增加文件大小限制
        maxWidth: 2000,  // 增加宽度限制
        maxHeight: 2000  // 增加高度限制
    },
    compression: {
        ...DEFAULT_CONFIG.compression,
        targetSizeKB: 100, // 二维码目标更小
        manual: {
            maxWidth: 300,
            maxHeight: 300,
            quality: 0.9, // 二维码需要更高质量
            outputFormat: 'image/png' // 二维码用PNG更好
        }
    }
};

// 根据环境获取配置
export function getUploadConfig(type = 'default') {
    const configs = {
        avatar: AVATAR_CONFIG,
        qrcode: QRCODE_CONFIG,
        default: DEFAULT_CONFIG
    };
    
    return configs[type] || DEFAULT_CONFIG;
}

// 根据网络状况调整策略
export function getStrategyByNetwork() {
    // 检测网络连接类型（如果支持）
    if (navigator.connection) {
        const connection = navigator.connection;
        const effectiveType = connection.effectiveType;
        
        // 慢速网络使用更激进的压缩
        if (effectiveType === 'slow-2g' || effectiveType === '2g') {
            return {
                strategy: UPLOAD_STRATEGY.COMPRESSED_BASE64,
                targetSizeKB: 50
            };
        } else if (effectiveType === '3g') {
            return {
                strategy: UPLOAD_STRATEGY.COMPRESSED_BASE64,
                targetSizeKB: 100
            };
        }
    }
    
    // 默认策略
    return {
        strategy: UPLOAD_STRATEGY.FILE_UPLOAD,
        targetSizeKB: 200
    };
}

// 根据设备性能调整策略
export function getStrategyByDevice() {
    // 检测设备内存（如果支持）
    if (navigator.deviceMemory) {
        const memory = navigator.deviceMemory;
        
        // 低内存设备使用更保守的策略
        if (memory <= 2) {
            return {
                strategy: UPLOAD_STRATEGY.COMPRESSED_BASE64,
                targetSizeKB: 100,
                maxConcurrent: 1
            };
        } else if (memory <= 4) {
            return {
                strategy: UPLOAD_STRATEGY.COMPRESSED_BASE64,
                targetSizeKB: 200,
                maxConcurrent: 2
            };
        }
    }
    
    // 默认策略
    return {
        strategy: UPLOAD_STRATEGY.FILE_UPLOAD,
        targetSizeKB: 200,
        maxConcurrent: 3
    };
}

// 智能策略选择
export function getSmartStrategy(fileSize, type = 'default') {
    const baseConfig = getUploadConfig(type);
    const networkStrategy = getStrategyByNetwork();
    const deviceStrategy = getStrategyByDevice();
    
    // 文件太大时强制压缩
    if (fileSize > 5 * 1024 * 1024) {
        return {
            ...baseConfig,
            strategy: UPLOAD_STRATEGY.COMPRESSED_BASE64,
            targetSizeKB: Math.min(networkStrategy.targetSizeKB, 100)
        };
    }
    
    // 综合网络和设备情况
    return {
        ...baseConfig,
        strategy: networkStrategy.strategy,
        targetSizeKB: Math.min(networkStrategy.targetSizeKB, deviceStrategy.targetSizeKB || 200)
    };
}

// 获取压缩质量建议
export function getQualityRecommendation(fileSize, targetSize) {
    const ratio = targetSize / fileSize;
    
    if (ratio >= 0.8) return 0.9;
    if (ratio >= 0.6) return 0.8;
    if (ratio >= 0.4) return 0.7;
    if (ratio >= 0.2) return 0.6;
    return 0.5;
}

// 批量上传配置
export const BATCH_UPLOAD_CONFIG = {
    maxConcurrent: 3,           // 最大并发数
    chunkSize: 1024 * 1024,     // 分片大小 1MB
    retryTimes: 3,              // 重试次数
    retryDelay: 1000,           // 重试延迟（毫秒）
    
    // 进度回调配置
    progress: {
        updateInterval: 100,     // 进度更新间隔（毫秒）
        showDetail: true         // 是否显示详细进度
    }
};
