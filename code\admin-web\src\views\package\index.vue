<template>
    <!-- 手机端套餐 -->
    <div class="inherit-platform">
        <div class="page-header">
            <div class="left">
                <div class="title">手机端套餐维护</div>
                <el-input v-model="searchKeyword" placeholder="请输入套餐名称" prefix-icon="el-icon-search" @input="handleSearch"
                    clearable></el-input>
            </div>
            <div class="right">
    
                <el-row :gutter="10">
    
    
                    <el-button type="primary" @click="getList()">刷新</el-button>
    
                    <el-button type="primary" @click="toAdd()">新增</el-button>
    
                </el-row>
            </div>
        </div>
    
        <el-table v-loading="loading" :data="dateList" border stripe highlight-current-row style="width: 100%"
            height="600px" :default-sort="{prop: 'sort', order: 'ascending'}">
            <el-table-column type="index" label="序号" align="center" />
            <el-table-column label="套餐名称" align="center" prop="name" />
            <el-table-column label="内容" align="center" prop="content" />
            <el-table-column label="类型" align="center" prop="leixing" />
            <el-table-column label="介绍" align="center" prop="intro" />
            <el-table-column label="原价" align="center" prop="pays" />
            <el-table-column label="实际价格" align="center" prop="scpays" />
            <el-table-column label="排序" align="center" prop="sort" sortable />
            <el-table-column label="状态" align="center" prop="status" />
            <el-table-column label="添加时间" align="center" prop="addtime" />
            <el-table-column label="操作" align="center" fixed="right">
                <template slot-scope="scope">
                    <el-button size="mini" type="primary" icon="el-icon-edit" plain
                        @click="handleEdit(scope.row)">编辑</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <div class="empty-box">
                    <img :src="emptyImg" alt="暂无数据" class="empty-img" />
                    <div class="empty-text">暂无数据</div>
                </div>
            </template>
        </el-table>
        <!-- 分页 -->
    
    
    
        <!-- 新增套餐 -->
        <el-dialog title="新增套餐" :visible.sync="openAddDialog" width="900px" top="5vh" :modal-append-to-body="false"
            :close-on-click-modal="false" class="custom-add-dialog">
            <!-- 上方表单 -->
            <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="90px" label-position="top"
                class="form-section">
                <div class="form-row">
                    <el-form-item label="套餐名称" prop="name">
                        <el-input v-model="addForm.name" placeholder="请输入套餐名称" clearable type="textarea"
                            :autosize="{ minRows: 1, maxRows: 10}" />
                    </el-form-item>
                    <el-form-item label="内容" prop="content">
                        <el-input v-model="addForm.content" placeholder="内容" clearable type="textarea"
                            :autosize="{ minRows: 1, maxRows: 10}" />
                    </el-form-item>
                </div>
                <div class="form-row">
                    <el-form-item label="类型" prop="leixing">
                        <el-input v-model="addForm.leixing" placeholder="类型" clearable type="textarea"
                            :autosize="{ minRows: 1, maxRows: 10}" />
                    </el-form-item>
                    <el-form-item label="介绍" prop="intro">
                        <el-input v-model="addForm.intro" placeholder="介绍" clearable type="textarea"
                            :autosize="{ minRows: 1, maxRows: 10}" />
                    </el-form-item>
                </div>
                <div class="form-row">
                    <el-form-item label="原价" prop="pays">
                        <el-input v-model="addForm.pays" placeholder="原价" clearable type="number" />
                    </el-form-item>
                    <el-form-item label="实际价格" prop="scpays">
                        <el-input v-model="addForm.scpays" placeholder="实际价格" type="number" clearable />
                    </el-form-item>
                    <el-form-item label="状态" prop="status">
                        <el-select v-model="addForm.status" placeholder="状态">
                            <el-option v-for="(item,index) in statusList" :key="index" :value="item.value"
                                :label="item.label">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="排序" prop="sort">
                        <el-input-number v-model="addForm.sort" placeholder="排序" :step="1" :min="1" step-strictly />
                    </el-form-item>
                </div>
            </el-form>
            <!-- 新增项目按钮 -->
            <div slot="footer">
                <el-button @click="openAddDialog = false">取消</el-button>
                <el-button type="primary" @click="handleAdd">确定</el-button>
            </div>
        </el-dialog>
        <!-- 修改套餐 -->
        <el-dialog title="套餐维护" :visible.sync="openUpdateDialog" width="60%" top="5vh" :modal-append-to-body="false"
            :close-on-click-modal="false" class="custom-add-dialog">
            <!-- 上方表单 -->
            <el-button type="primary" plain size="mini" @click="addTableItem">新增套餐内项目</el-button>
            <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="90px" label-position="top"
                class="form-section">
                <div class="form-row">
                    <el-form-item label="套餐名称" prop="name">
                        <el-input v-model="addForm.name" placeholder="请输入套餐名称" clearable type="textarea"
                            :autosize="{ minRows: 1, maxRows: 10}" />
                    </el-form-item>
                    <el-form-item label="内容" prop="content">
                        <el-input v-model="addForm.content" placeholder="内容" clearable type="textarea"
                            :autosize="{ minRows: 1, maxRows: 10}" />
                    </el-form-item>
                    <el-form-item label="类型" prop="leixing">
                        <el-input v-model="addForm.leixing" placeholder="类型" clearable type="textarea"
                            :autosize="{ minRows: 1, maxRows: 10}" />
                    </el-form-item>
                    <el-form-item label="介绍" prop="intro">
                        <el-input v-model="addForm.intro" placeholder="介绍" clearable type="textarea"
                            :autosize="{ minRows: 1, maxRows: 10}" />
                    </el-form-item>
                </div>
                <div class="form-row">
                    <el-form-item label="原价" prop="pays">
                        <el-input v-model="addForm.pays" placeholder="原价" clearable type="number" />
                    </el-form-item>
                    <el-form-item label="实际价格" prop="scpays">
                        <el-input v-model="addForm.scpays" placeholder="实际价格" type="number" clearable />
                    </el-form-item>
                    <el-form-item label="状态" prop="status">
    
                        <el-select v-model="addForm.status" placeholder="状态">
                            <el-option v-for="(item,index) in statusList" :key="index" :value="item.value"
                                :label="item.label">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="排序" prop="sort">
                        <el-input-number v-model="addForm.sort" placeholder="排序" :step="1" :min="1" step-strictly />
                    </el-form-item>
                </div>
            </el-form>
            <!-- 套餐内项目表格 -->
            <el-table :data="tableData" border style="width: 100%; margin: 15px 0;" max-height="400px">
                <el-table-column type="index" label="序号" align="center" />
                <el-table-column prop="name" label="项目名称" align="center" min-width="120">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.name" placeholder="请输入内容" size="small" type="textarea"
                            :autosize="{ minRows: 1, maxRows: 10}" />
                    </template>
                </el-table-column>
                <el-table-column prop="code" label="项目编码" align="center" min-width="100">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.code" placeholder="请输入内容" size="small" type="textarea"
                            :autosize="{ minRows: 1, maxRows: 10}" />
                    </template>
                </el-table-column>
                <el-table-column prop="content" label="项目内容" align="center" min-width="150">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.content" placeholder="请输入内容" size="small" type="textarea"
                            :autosize="{ minRows: 1, maxRows: 10}" />
                    </template>
                </el-table-column>
                <el-table-column prop="sense" label="临床意义" align="center" min-width="150">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.sense" placeholder="请输入内容" size="small" type="textarea"
                            :autosize="{ minRows: 1, maxRows: 10}" />
                    </template>
                </el-table-column>
    
                <el-table-column label="操作" align="center" min-width="160">
                    <template slot-scope="scope">
                        <el-button size="mini" type="primary" icon="el-icon-success" plain
                            @click="insertTableItem(scope.$index, scope.row)"></el-button>
                        <el-button size="mini" type="danger" icon="el-icon-delete" plain
                            @click="removeTableItem(scope.$index, scope.row)"></el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 确定项目按钮 -->
            <div slot="footer">
                <el-button @click="openUpdateDialog = false">取消</el-button>
                <el-button type="primary" @click="handleUpdate">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>

import {
  InsertProject, RemoveProject, SelectProject, InsertPackage, RemovePackage, SelectPackage
} from "@/api/integra/package"
export default {
  name: 'Index',
  data() {
    return {
      searchKeyword: '',
      selectDiag: [],
      searchList: [],
      stasus: '',
      loading: false,
      dateList: [],
      emptyImg: require('@/assets/images/1.png'),
      openUpdateDialog: false,
      openAddDialog: false,
      diagnosisDict: [],
      addForm: {
        id: '',
        name: '',
        content: '',
        leixing: '',
        intro: '',
        pays: 0,
        scpays: 0,
        sort: 0,
        status: '上架',
      },
      addRules: {

      },
      queryParams: {
        inputCode: undefined,
        pageSize: 10,
        pageNum: 1,
      },
      total: 0,
      examDict: [],
      statusList: [
        { label: '上架', value: '上架' }, { label: '下架', value: '下架' },
      ],
      tableData: [],
      itemForm: {},
    }
  },

  computed: {

  },

  created() {
    this.getList()

  },

  methods: {
    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        SelectPackage(this.queryParams).then(res => {
          if (res && res.data) {
            this.dateList = res.data
          }
        })
      } catch (error) {
        this.$message({
          showClose: true,
          message: '操作失败',
          type: 'error'
        })
      }
      this.loading = false
    },
    // 编辑项目
    handleEdit(row) {
      // 将表格行数据填充到表单中
      this.stasus = 'update'
      this.addForm = { ...this.addForm, ...row }
      this.gainItems(row)
      this.openUpdateDialog = true
    },
    gainItems(row) {
      try {
        SelectProject(row).then(res => {
          if (res && res.data) {
            this.tableData = res.data
          }
        })
      } catch (error) {
        this.$message({
          showClose: true,
          message: '操作失败',
          type: 'error'
        })
      }
    },

    // 新增套餐内项目
    addTableItem() {
      // 新增一行数据，默认携带当前套餐ID
      this.tableData.push({
        id: '',
        name: '',
        code: '',
        content: '',
        sense: '',
        packageId: this.addForm.id || '' // 关联当前套餐ID
      })
    },

    // 删除套餐内项目
    removeTableItem(index, row) {
      try {
        RemoveProject(row).then(res => {
          this.gainItems(this.addForm)
          this.$message({
            showClose: true,
            message: '操作成功',
            type: 'success'
          })
        })
      } catch (error) {
        this.$message({
          showClose: true,
          message: '操作失败',
          type: 'error'
        })

      }

    },
    // 新增/修改套餐内项目
    insertTableItem(index, row) {
      this.itemForm = { ...this.itemForm, ...row }
      this.itemForm.packageId = this.addForm.id
      try {
        InsertProject(this.itemForm).then(res => {
          this.tableData = res.data
          this.$message({
            showClose: true,
            message: '操作成功',
            type: 'success'
          })
          this.gainItems(this.addForm)
        })
      } catch (error) {
        this.$message({
          showClose: true,
          message: '获取列表失败',
          type: 'error'
        })
      }

    },
    handleSearch() {
      if (this.searchKeyword) {
        this.dateList = this.dateList.filter(item =>
          item.name.toLowerCase().includes(this.searchKeyword.toLowerCase())

        );
      } else {
        this.getList(); // 清空搜索时重新加载全部数据
      }
    },


    handleSizeChange(val) {

      this.queryParams.pageSize = val;
      this.getList();

    },
    handleCurrentChange(val) {

      this.queryParams.pageNum = val;
      this.getList();

    },
    toAdd() {
      this.stasus = 'add'
      this.addForm = {
        id: '',
        name: '',
        content: '',
        leixing: '',
        intro: '',
        pays: 0,
        scpays: 0,
        sort: 0,
        status: '上架',
      },
        this.openAddDialog = true
    },
    // 新增项目
    handleAdd() {
      console.log(this.addForm);
      this.$refs.addFormRef.validate(valid => {
        if (!valid) return
        // 这里可以调用AddDiagnosis接口进行保存
        InsertPackage(this.addForm).then(res => {
          if (res && res.code === 200) {
            this.$message.success('新增成功')
            this.openAddDialog = false
            this.getList && this.getList()
          } else {
            this.$message.error(res.msg || '新增失败')
          }
        })
      })
    },






    handleUpdate() {
      this.$refs.addFormRef.validate(valid => {
        if (!valid) return
        // 这里可以调用AddDiagnosis接口进行保存
        InsertPackage(this.addForm).then(res => {
          if (res && res.code === 200) {
            this.$message.success('操作成功')
            this.openUpdateDialog = false
            this.getList && this.getList()
          } else {
            this.$message.error(res.msg || '操作成功')
          }
        })
      })
    },

  }
}
</script>

<style lang="scss" scoped>
// 主容器样式
.inherit-platform {
    padding: 24px;
    background-color: #f6f8fa;
    min-height: calc(100vh - 84px);
}

// 页面头部样式
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 0 8px;

    .left {
        .title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2329;
            margin-bottom: 8px;
        }

        .subtitle {
            font-size: 14px;
            color: #86909c;
        }
    }

    .right {
        .el-button {
            padding: 12px 24px;
            font-size: 14px;
            border-radius: 8px;
            background: #409eff;
            border: none;
            color: #fff;
            transition: all 0.3s;

            &:hover {
                background: #66b1ff;
                box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
            }
        }
    }
}

// 空数据样式
.empty-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;

    .empty-img {
        width: 80px;
        height: 80px;
        margin-bottom: 12px;
        opacity: 0.7;
    }

    .empty-text {
        color: #999;
        font-size: 16px;
    }
}

.custom-add-dialog .el-dialog {
    border-radius: 14px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
}

.custom-add-dialog .el-dialog__header {
    background: #f5f7fa;
    border-radius: 14px 14px 0 0;
    padding: 18px 24px 10px 24px;
}

.custom-add-dialog .el-dialog__body {
    padding: 24px 32px 10px 32px;
    background: #fff;
}

.custom-add-dialog .el-form-item {
    margin-bottom: 22px;
}

.custom-add-dialog .el-input__inner {
    border-radius: 7px;
    height: 38px;
    font-size: 15px;
}

.custom-add-dialog .el-select .el-input__inner {
    border-radius: 7px;
}

.custom-add-dialog .el-dialog__footer {
    padding: 12px 32px 24px 32px;
    background: #f5f7fa;
    border-radius: 0 0 14px 14px;
    text-align: right;
}

.custom-add-dialog .el-button {
    min-width: 80px;
    border-radius: 7px;
    font-size: 15px;
    margin-left: 12px;
}

// 表单区域样式
.form-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.form-row {
    display: flex;
    gap: 10px;
    margin-bottom: 1px;
}

.form-row .el-form-item {
    flex: 1;
    margin-bottom: 0;
}

.form-section .el-form-item {
    margin-bottom: 16px;
}

.form-section .el-input__inner,
.form-section .el-select .el-input__inner {
    border-radius: 6px;
    height: 36px;
    font-size: 14px;
}

// 表格区域样式
.table-section {
    background: #fff;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.table-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2329;
}

.table-count {
    font-size: 14px;
    color: #86909c;
}

.table-section .el-table {
    border: none;
}

.table-section .el-table th {
    background: #f8f9fa;
    color: #1f2329;
    font-weight: 600;
}

.table-section .el-table td {
    padding: 8px 0;
}
</style>