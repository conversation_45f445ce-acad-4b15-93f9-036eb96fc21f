import request from '@/utils/request'

export function SaveHealthManagements(param) {
  return request({
    url: 'HealthManagementsQueen/SaveHealthManagements',
    method: 'post',
    data: param
  })
}

export function QueryHealthManagementsTypeAsPolicy(param) {
  return request({
    url: 'HealthManagementsQueen/QueryHealthManagementsTypeAsPolicy',
    method: 'get',
    params: param
  })
}

export function QueryHealthManagementsTypeAsPopularization(param) {
  return request({
    url: 'HealthManagementsQueen/QueryHealthManagementsTypeAsPopularization',
    method: 'get',
    params: param
  })
}

export function DeleteHealthManagements(id) {
  return request({
    url: 'HealthManagementsQueen/DeleteHealthManagements?id='+id,
    method: 'post',
  })
}

export function UpdateHealthManagements(param) {
  return request({
    url: 'HealthManagementsQueen/UpdateHealthManagements',
    method: 'post',
    data: param
  })
}
