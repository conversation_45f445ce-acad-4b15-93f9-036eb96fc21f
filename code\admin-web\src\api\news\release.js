import request from '@/utils/request'
// 附件上传
export function UploadFile(param) {
  return request({
    url: 'NewsQueen/UploadFile',
    method: 'post',
    data: param
  })
}

export function SaveNews(param) {
  return request({
    url: 'NewsQueen/SaveNews',
    method: 'post',
    data: param
  })
}

export function QueryNews(param) {
  return request({
    url: 'NewsQueen/QueryNews',
    method: 'get',
    params: param
  })
}

export function DeleteNews(id) {
  return request({
    url: 'NewsQueen/DeleteNews?id='+id,
    method: 'post',
  })
}

export function UpdateNews(param) {
  return request({
    url: 'NewsQueen/UpdateNews',
    method: 'post',
    data: param
  })
}
