import request from '@/utils/request'
const baseURL = 'Assistant/'

// 查询助理列表
export function ListAssistant(query) {
    return request({
        url: baseURL + 'ListAssistant',
        method: 'get',
        params: query
    })
}

// 查询助理详细信息
export function GetAssistant(id) {
    return request({
        url: baseURL + 'GetAssistant/' + id,
        method: 'get'
    })
}

// 新增助理
export function AddAssistant(data) {
    return request({
        url: baseURL + 'AddAssistant',
        method: 'post',
        data: data
    })
}

// 修改助理
export function UpdateAssistant(data) {
    return request({
        url: baseURL + 'UpdateAssistant',
        method: 'post',
        data: data
    })
}

// 删除助理
export function DelAssistant(id) {
    return request({
        url: baseURL + 'DelAssistant/' + id,
        method: 'get'
    })
}
