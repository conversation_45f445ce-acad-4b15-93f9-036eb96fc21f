<template>
  <div class=" app-container">
    <!-- 主表操作区 -->
    <div class="header-box">
      <div class="search-box">
        <el-row>
          <el-col :span="5">
            <el-input v-model="queryParam.input" placeholder="输入策略名称/类型搜索" clearable style="width: 300px"
              @input="getList" />
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="getList">
              查询
            </el-button>
            <el-button type="primary" @click="handleAddMaster">新增策略</el-button>
  
  
          </el-col>
          <el-col :span="6" v-show="master.name">
            <el-button type="primary" plain>
              {{ '策略名称：'+ master.name}}
            </el-button>
  
          </el-col>
          <el-col :span="6"> <el-button type="primary" @click="handleAddDetail">
              新增详情
            </el-button>
          </el-col>
  
  
        </el-row>
  
  
  
      </div>
    </div>
    <el-row>
      <el-col :span="11"> <!-- 主表 -->
        <el-table :data="ployMasters" highlight-current-row @current-change="handleMasterSelect" style="width: 100%"
          border height="600">
          <el-table-column prop="name" label="策略名称" width="180" />
          <el-table-column prop="points" label="所需积分" width="120" />
          <el-table-column prop="type" label="类型" width="120">
            <template #default="{ row }">
              <el-tag :type="row.type === '基础兑换' ? '' : 'warning'">
                {{ row.type }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === '0' ? '' : 'warning'">
                {{ row.status === '0' ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="{ row }">
              <el-button size="mini" type="text" @click="handleDetail(row)">详情</el-button>
              <el-button size="mini" type="text" @click="handleEditMaster(row)">编辑</el-button>
              <el-button size="mini" type="text" @click="handleDeleteMaster(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="12">
        <!-- 详情表 -->
        <div>
          <el-table :data="ployDetails" border height="600">
            <el-table-column prop="itemName" label="项目名称" width="200" />
            <el-table-column prop="itemCode" label="项目编码" width="150" />
            <el-table-column prop="type" label="类型" width="120">
              <template #default="{ row }">
                {{ typeMapping[row.type] || row.type }}
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" />
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button size="mini" type="text" @click="handleUpdateDetail(row)">修改</el-button>
                <el-button size="mini" type="text" @click="handleDeleteDetail(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>
  
  
  
  
    <!-- 主表编辑对话框 -->
    <el-dialog :title="masterDialogTitle" :visible.sync="masterDialogVisible" width="40%">
      <el-form :model="currentEditMaster" label-width="100px">
        <el-form-item label="策略名称">
          <el-input v-model="currentEditMaster.name" />
        </el-form-item>
        <el-form-item label="所需积分">
          <el-input-number v-model="currentEditMaster.points" :min="0" :step="100" />
        </el-form-item>
        <el-form-item label="策略类型">
          <el-select v-model="currentEditMaster.type">
            <el-option label="基础兑换" value="基础兑换" />
            <el-option label="稀缺权益" value="稀缺权益" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="masterDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveMaster">保存</el-button>
      </span>
    </el-dialog>
    <!-- 详情表编辑对话框 -->
    <el-dialog :title="detailDialogTitle" :visible.sync="detailDialogVisible" width="40%">
      <el-form :model="currentEditDetail" label-width="100px">
        <el-form-item label="项目名称">
          <el-input v-model="currentEditDetail.itemName" />
        </el-form-item>
        <el-form-item label="项目代码">
          <el-input v-model="currentEditDetail.itemCode" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="currentEditDetail.description" />
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="currentEditDetail.type">
            <el-option label="体检项目" value="体检项目" />
            <el-option label="贵宾卡" value="贵宾卡" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="detailDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveDetail">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  GainPloyMaster,
  GainPloyDetail,
  RemovePloyMaster,
  RemovePloyDetail,
  SetPloyMaster,
  SetPloyDetail
} from '@/api/integra/integraCard'

export default {
  data() {
    return {
      detailDialogTitle: '策略详情变动',
      currentEditDetail: {},
      detailDialogVisible: false,
      queryParam: {
        input: undefined,
      },
      master: {},
      searchKey: '',
      currentMaster: null,
      masterDialogVisible: false,
      masterDialogTitle: '新增策略',
      typeMapping: {
        '体检项目': '体检项目',
        '贵宾卡': '贵宾卡'
      },

      // 假数据
      ployMasters: [

      ],

      ployDetails: [

      ],

      currentEditMaster: this.getEmptyMaster()
    }
  },

  computed: {

  },
  created() {

  },
  mounted() {
    this.getList()
  },
  methods: {

    getList() {

      GainPloyMaster(this.queryParam).then(res => {
        this.ployMasters = res.data
      })
    },
    handleDetail(master) {
      if (master) {
        this.master = master
      }
      GainPloyDetail(this.master).then(res => {
        this.ployDetails = res.data
      })
    },
    handleDeleteMaster(row) {
      this.$confirm('确认删除该策略？').then(() => {
        RemovePloyMaster(row).then(res => {
          this.$message.success('删除成功')
          this.getList()
        })
      })
    },
    handleDeleteDetail(row) {
      this.$confirm('确认删除该策略？').then(() => {
        RemovePloyDetail(row).then(res => {
          this.handleDetail()
          this.$message.success('删除成功')
        })
      })


    },
    // 主表操作
    handleMasterSelect(master) {
      this.currentMaster = master
    },

    handleAddMaster() {
      this.currentEditMaster = this.getEmptyMaster()
      this.masterDialogTitle = '新增策略'
      this.masterDialogVisible = true
    },

    handleEditMaster(row) {
      this.currentEditMaster = { ...row }
      this.masterDialogTitle = '编辑策略'
      this.masterDialogVisible = true
    },
    handleUpdateDetail(row) {
      this.currentEditDetail = { ...row }
      this.detailDialogVisible = true
      console.log(this.currentEditDetail)
    },


    saveMaster() {
      if (!this.currentEditMaster.name) {
        return this.$message.warning('请输入策略名称')
      }
      console.log(this.currentEditMaster);


      SetPloyMaster(this.currentEditMaster).then(res => {
        this.getList()
        this.$message.success('保存成功')

      })


      this.masterDialogVisible = false
    },

    // 新增详情操作
    handleAddDetail() {
      if (!this.master.name) {
        return this.$message.warning('请先选择策略')
      }
      this.detailDialogVisible = true
      this.currentEditDetail = {
        id: '',
        itemName: '',
        itemCode: '',
        description: '',
        mastId: this.currentMaster.id,
        type: '体检项目'
      }
    },
    saveDetail() {
      SetPloyDetail(this.currentEditDetail).then(res => {
        this.handleDetail()
        this.$message.success('操作成功')

      })
      this.detailDialogVisible = false
    },


    // 状态切换
    handleStatusChange(row) {
      this.$message.success(`状态已${row.status === 0 ? '启用' : '停用'}`)
    },

    getEmptyMaster() {
      return {
        id: '',
        name: '',
        points: 0,
        type: '基础兑换',
        status: 0
      }
    }
  }
}
</script>

<style scoped>

</style>