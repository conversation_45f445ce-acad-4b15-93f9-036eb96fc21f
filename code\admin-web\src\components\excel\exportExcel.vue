<!--
 * FilePath     : \src\components\excel\exportExcel.vue
 * Author       : 苏军志
 * Date         : 2022-04-17 16:45
 * LastEditors  : 苏军志
 * LastEditTime : 2022-05-11 17:33
 * Description  : 导入Excel
 * CodeIterationRecord:
-->
<template>
  <div class="export-excel" @click="exportExcel">
    <slot name="trigger">
      <el-button class="download-button iconfont icon-download-fill">导出Excel</el-button>
    </slot>
  </div>
</template>

<script>
import { export_json_to_excel } from "@/utils/excel/Export2Excel.js";
export default {
  props: {
    // excel 文件名
    excelName: {
      type: String,
      default: "excelData",
    },
    // Excel列名和字段名对应关系
    columnMap: {
      type: Array,
      required: true,
      default: () => [],
    },
    // excel对应数据
    excelData: {
      type: Array,
      required: true,
      default: () => [],
    },
    // 列宽自适应
    autoWidth: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    exportExcel() {
      let excelColumns = [];
      let excelFileds = [];
      this.columnMap.forEach((column) => {
        if (column) {
          excelColumns.push(column.label);
          excelFileds.push(column.key);
        }
      });
      let dataMain = this.formatJson(excelFileds, this.excelData);
      export_json_to_excel(excelColumns, dataMain, this.excelName, null, this.autoWidth);
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((jsonDataItem) =>
        filterVal.map((filterValItem) => {
          return jsonDataItem[filterValItem];
        })
      );
    },
  },
};
</script>

<style>
.export-excel {
  display: inline-block;
}
</style>
