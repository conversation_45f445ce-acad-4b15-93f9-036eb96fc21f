<template>
  <div>
    <div v-if="type === '1'">
      <div style="border: 1px solid #ccc;">
        <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig" :mode="mode"/>
        <Editor style="height: 700px; overflow-y: hidden;" v-model="addForm.html" :defaultConfig="editorConfig"
                :mode="mode"
                @onCreated="onCreated"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </div>

    <div v-if="type === '2'">
      <div style="border: 1px solid #ccc;">
        <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig" :mode="mode"/>
        <Editor style="height: 700px; overflow-y: hidden;" v-model="updateForms.html" :defaultConfig="editorConfig"
                :mode="mode"
                @onCreated="onCreated"/>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="newsUpdate">确 定</el-button>
        <el-button @click="cancelTwo">取 消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { md5 } from '@/utils/md5'
import { UploadFile } from '@/api/news/release'
import {SaveArchiveInfoAfter, UpdateArchiveInfoAfter} from "../../api/archiveInfomation/archiveInfomation";
export default {
  name: 'htmlRedact',
  props: ['formData', 'type','baseURL','updateForms'],
  components: { Editor, Toolbar },
  data() {
    return {
      mode: 'default',
      editor: null,
      toolbarConfig: {},
      editorConfig: {
        MENU_CONF: {
          uploadImage: {
            customUpload: async(file, insertFn) => {
              const loading = this.$loading({
                lock: true,
                text: '休息一下,文件上传中(●' + '◡' + '●)',
                spinner: 'el-icon-coffee-cup',
                background: 'rgba(0, 0, 0, 0.7)'
              })
              const param = new FormData()
              param.append('file', file)
              md5(file).then(res => {
                param.append('md5', res)
                UploadFile(param).then(response => {
                  insertFn(this.baseURL + response.data.path)
                  loading.close()
                })
              })
            }
          },
          uploadVideo: {
            customUpload: async(file, insertFn) => {
              const loading = this.$loading({
                lock: true,
                text: '休息一下,文件上传中(●' + '◡' + '●)',
                spinner: 'el-icon-coffee-cup',
                background: 'rgba(0, 0, 0, 0.7)'
              })
              const param = new FormData()
              param.append('file', file)
              md5(file).then(res => {
                param.append('md5', res)
                UploadFile(param).then(response => {
                  insertFn(this.baseURL + response.data.path)
                  loading.close()
                })
              })
            }
          }
        }
      },
      addForm: {}
    }
  },
  created() {
    this.editor = null;
    this.mode = 'default';
    this.toolbarConfig = {};
    if (this.formData) {
      this.addForm = this.formData
    }
  },
  mounted() {
  },
  methods: {
    // 数据提交
    submitForm() {
      SaveArchiveInfoAfter(this.addForm).then(res => {
        if (res.code === 200){
          this.$message.success(res.message)
          this.$emit('refresh', true)
        }
      })
    },
    // 文本修改
    newsUpdate(){
      UpdateArchiveInfoAfter(this.updateForms).then(res => {
        if (res.code === 200){
          this.$message.success(res.message);
          this.cancelTwo();
        }
      })
    },
    // 取消按钮
    cancel() {
      this.$emit('html-button', true)
    },
    cancelTwo() {
      this.$emit('cancel-event-two', true)
    },
    // 文本域点击
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
    }
  }
}
</script>

<style scoped lang="scss">

</style>
<style src="@wangeditor/editor/dist/css/style.css"></style>
