
.single-master{
  padding: 0;
  margin: 0;
  .single-title{
    background-color: #DFECFD;
    color: #2D3849;
    font-size: 1.7em;
    display: flex;
    justify-content: center;
    align-items: center;
    letter-spacing: 1em;
  }
  .element-page{
    display: flex;
    justify-content: center;
  }
  .single-element{
    border: 10px solid #DFECFD;
    border-top: 1px;
  }
  .element-master{
    padding: 10px;

  }
  .element-item{
    border: 1px solid #DFECFD;
    display: flex;
  }
  .element-form{
    margin-top: 5px;
    .el-form-item {
      margin-bottom: 10px;
    }
    ::v-deep.el-input__inner {
      padding: 0 13px;
    }
    ::v-deep.el-input--medium .el-input__inner {
      height: 33px;
      line-height: 36px;
    }
    ::v-deep.el-input--suffix .el-input__inner {
      padding-right: 25px;
      padding-left: 30px;
    }
    ::v-deep.el-date-editor.el-input, .el-date-editor.el-input__inner {
      width: 150px;
    }
    ::v-deep.el-input--medium .el-input__inner {
      width: 150px;
      height: 33px;
      line-height: 36px;
    }
  }
  .element-table{
    ::v-deep.el-table--medium .el-table__cell {
      padding: 8px 0;
    }

    ::v-deep.el-table .cell {
      padding-left: 4px;
      padding-right: 4px;
    }

    ::v-deep.el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
      font-size: 16px;
      height: 30px;
    }

    ::v-deep.el-table {
      font-size: 14px;
    }
    ::v-deep.el-table__body tr.current-row > td.el-table__cell, .el-table__body tr.selection-row > td.el-table__cell {
      background-color: #1890FF;
      color: #FFFFFF;
    }
    ::-webkit-scrollbar {
      width: 11px;
      height: 11px;
    }
  }
  .element-button{
    .el-button--medium {
      padding: 10px 20px;
      font-size: 14px;
      border-radius: 4px;
    }
  }
  .element-radio{
    .el-radio {
      font-size: 14px;
      margin-right: 15px;
    }
  }
  .element-drawer{
    ::v-deep.el-drawer__header {
      color: #2D3849 !important;
      background-color: #DFECFD;
      letter-spacing: 1em;
      font-size: 22px !important;
    }
  }
  @media screen and (max-width: 1250px) {
    .single-title {
      font-size: 1.5em;
    }
    .element-form{
      margin-top: 4px;
      .el-form-item {
        margin-bottom: 8px;
      }
      ::v-deep.el-input__inner {
        padding: 0 12px;
      }
      ::v-deep.el-input--medium .el-input__inner {
        height: 31px;
        line-height: 36px;
      }
      ::v-deep.el-input--suffix .el-input__inner {
        padding-right: 20px;
        padding-left: 20px;
      }
      ::v-deep.el-date-editor.el-input, .el-date-editor.el-input__inner {
        width: 130px;
      }
      ::v-deep.el-input--medium .el-input__inner {
        width: 130px;
        height: 31px;
        line-height: 36px;
      }
    }
    .element-table{
      ::v-deep.el-table--medium .el-table__cell {
        padding: 6px 0;
      }

      ::v-deep.el-table .cell {
        padding-left: 3px;
        padding-right: 3px;
      }

      ::v-deep.el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
        font-size: 13px;
        height: 30px;
      }

      ::v-deep.el-table {
        font-size: 13px;
      }
    }
    .element-button{
      .el-button--medium {
        padding: 9px 13px;
        font-size: 13px;
        border-radius: 4px;
      }
    }
    .element-radio{
      .el-radio {
        font-size: 13px;
        margin-right: 13px;
      }
    }
  }
  @media screen and (max-width: 1100px) {
    .single-title {
      font-size: 1.3em;
    }
    .element-form{
      margin-top: 3px;
      .el-form-item {
        margin-bottom: 6px;
      }
      ::v-deep.el-input__inner {
        padding: 0 10px;
      }
      ::v-deep.el-input--medium .el-input__inner {
        height: 29px;
        line-height: 36px;
      }
      ::v-deep.el-input--suffix .el-input__inner {
        padding-right: 16px;
        padding-left: 15px;
      }
      ::v-deep.el-date-editor.el-input, .el-date-editor.el-input__inner {
        width: 110px;
      }
      ::v-deep.el-input--medium .el-input__inner {
        width: 110px;
        height: 29px;
        line-height: 36px;
      }
    }
    .element-table{
      ::v-deep.el-table--medium .el-table__cell {
        padding: 4px 0;
      }

      ::v-deep.el-table .cell {
        padding-left: 2px;
        padding-right: 2px;
      }

      ::v-deep.el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
        font-size: 12px;
        height: 30px;
      }

      ::v-deep.el-table {
        font-size: 12px;
      }
    }
    .element-button{
      .el-button--medium {
        padding: 8px 11px;
        font-size: 12px;
        border-radius: 4px;
      }
    }
    .element-radio{
      .el-radio {
        font-size: 12px;
        margin-right: 11px;
      }
    }
  }
  @media screen and (max-width: 600px) {
    .single-title {
      font-size: 1em;
    }
    .element-form{
      margin-top: 2px;
      .el-form-item {
        margin-bottom: 4px;
      }
      ::v-deep.el-input__inner {
        padding: 0 5px;
      }
      ::v-deep.el-input--medium .el-input__inner {
        height: 27px;
        line-height: 36px;
      }
      ::v-deep.el-input--suffix .el-input__inner {
        padding-right: 10px;
        padding-left: 15px;
      }
      ::v-deep.el-date-editor.el-input, .el-date-editor.el-input__inner {
        width: 90px;
      }
      ::v-deep.el-input--medium .el-input__inner {
        width: 90px;
        height: 27px;
        line-height: 36px;
      }
    }
    .element-table{
      ::v-deep.el-table--medium .el-table__cell {
        padding: 2px 0;
      }

      ::v-deep.el-table .cell {
        padding-left: 1px;
        padding-right: 1px;
      }

      ::v-deep.el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
        font-size: 11px;
        height: 30px;
      }

      ::v-deep.el-table {
        font-size: 11px;
      }
    }
    .element-button{
      .el-button--medium {
        padding: 7px 7px;
        font-size: 11px;
        border-radius: 4px;
      }
    }
    .element-radio{
      .el-radio {
        font-size: 11px;
        margin-right: 9px;
      }
    }
  }
}
