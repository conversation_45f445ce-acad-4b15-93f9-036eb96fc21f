<template>
  <div class="app-container">
     <el-row :gutter="10">
       <el-col  :span="24" :xs="24">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="项目名称" prop="expertName">
        <el-input
          v-model="queryParams.comboName"
          placeholder="请输入项目名称"
          clearable
          style="width: 240px;"
          @change="getList"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="getList"
        >
          搜索
        </el-button>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="handleAdd"   size="mini" >新增</el-button>
     </el-form-item>
   
    </el-form>
    </el-col>
    <!-- 新增 -->
   

    <el-table
      ref="table"
      :data="
        comboList != null
          ? comboList.slice(
              (currentPage - 1) * pageSize,
              currentPage * pageSize,
            )
          : comboList
      "
      border
       :default-sort = "{prop: 'combo_name', order: 'descending'}"
         :header-cell-style="{ 'text-align': 'center', height: '20px' }"
        :cell-style="{ 'text-align': 'center' }"
    >
      <el-table-column
        label="套餐名称"
        align="center"
        prop="combo_name"
        :show-overflow-tooltip="true"
        sortable
      >
        <!-- <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expirationDate, "{y}-{m}-{d}") }}</span>
        </template> -->
        <template slot-scope="scope">
          <span>
            <el-input
              v-model="scope.row.combo_name"
              @change="itemChange(scope.row)"
            ></el-input>
          </span>
        </template>
      </el-table-column>

      <el-table-column
        label="原价"
        align="center"
        prop="price"
        :show-overflow-tooltip="true"
        sortable
      >
        <template slot-scope="scope">
          <span>
            <el-input-number
              v-model="scope.row.price"
              @change="itemChange(scope.row)"
              :step="0.1"
              :min="0"
            ></el-input-number>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="现价"
        align="center"
        prop="reall_price"
        :show-overflow-tooltip="true"
        sortable
      >
        <template slot-scope="scope">
          <span>
            <el-input-number
              v-model="scope.row.reall_price"
              @change="itemChange(scope.row)"
              :step="0.1"
              :min="0"
            ></el-input-number>
          </span>
        </template>
      </el-table-column>
        <el-table-column
        label="适用人群"
        align="center"
        prop="croud_suitable"
        :show-overflow-tooltip="true"
       sortable
      >
        <template slot-scope="scope">
          <span>
            <el-select
              clearable
              v-model="scope.row.croud_suitable"
              @change="itemChange(scope.row)"
            >
              <el-option
                v-for="item in suitOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </span>
        </template>
      </el-table-column>
         <el-table-column
              :show-overflow-tooltip="true"
              prop="iamge_url"
              label="图片"
            >
              <template slot-scope="scope">
                  <el-popover placement="left" title="" trigger="hover">
                    <el-image
                      :src="baseUrl + scope.row.iamge_url"
                      style="max-height: 500px; max-width: 800px;"
                    ></el-image>
                    <el-image
                      slot="reference"
                      :src="baseUrl + scope.row.iamge_url"
                      :alt="baseUrl + scope.row.iamge_url"
                      style="max-height: 60px; max-width: 40px;"
                    ></el-image>
                  </el-popover>
              
              </template>
            </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="gainListContact(scope.row)"
          >
            分配项目
          </el-button>
           <el-button
            size="mini"
            type="text"
            @click="toSetImages(scope.row)"
          >
            上传图片
          </el-button>
          <el-button size="mini" type="text" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-col :span="14" :offset="10">
      <!-- 分页 -->
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 40, 80, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="comboList == null ? 0 : comboList.length"
      ></el-pagination>
    </el-col>
</el-row>
    <!-- 新增对话框         :disabled="thisDisabled"-->
    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="套餐名称：" prop="itemName">
              <el-input
                v-model="form.comboName"
                placeholder="请输入套餐名称："
                clearable
                style="width: 200px;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原价">
              <el-input-number
                v-model="form.price"
                placeholder="请输入原价"
                clearable
                :step="0.1"
                :min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优惠价格">
              <el-input-number
                :step="0.1"
                :min="0"
                v-model="form.reallPrice"
                placeholder="请输入优惠价格"
                clearable
              />
            </el-form-item>
          </el-col>
            <el-col :span="12">
            <el-form-item label="适用人群">
              <el-radio-group v-model="form.croudSuitable">
                <el-radio
                  v-for="dict in suitOption"
                  :key="dict.value"
                  :label="dict.value"
                >
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-drawer
      title=""
      :visible.sync="contactOpen"
      direction="rtl"
      size="100%"
      :before-close="handleCollectClose"
      :show-close="showclose"
      @close="pdfVisiableClose"
    >
      <el-button
        type="primary"
        icon="el-icon-close"
        size="mini"
        @click="clickPdfClose"
        class="xuanfu"
      >
        关闭
      </el-button>
      <div v-loading="loading">
        <el-row>
          <el-col :span="6" :offset="4">
            <el-select v-model="value" placeholder="请选择" filterable multiple>
              <el-option
                v-for="item in itemsList"
                :key="item.id"
                :label="item.item_name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-col>
          <el-col :span="6" :offset="4">
            <el-button type="primary" @click="addItems">
              添加
            </el-button>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <el-tag>套餐名称：{{ contactModel.combo_name }}</el-tag>
        <el-table ref="table" :data="contactList" border>
          <el-table-column
            :show-overflow-tooltip="true"
            label="序号"
            align="center"
            min-width="30"
          >
            <template slot-scope="scop">
              {{ scop.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column
            label="检查项目"
            align="center"
            prop="itemName"
            :show-overflow-tooltip="true"
          ></el-table-column>
          <el-table-column
            label="基本内容"
            align="center"
            prop="itemContent"
            :show-overflow-tooltip="true"
          ></el-table-column>
          <el-table-column
            label="临床意义"
            align="center"
            prop="itemSense"
            :show-overflow-tooltip="true"
          ></el-table-column>
          <el-table-column
            label="适用人群"
            align="center"
            prop="croudSuitable"
            :show-overflow-tooltip="true"
          ></el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="delContact(scope.row)">
                移出
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>
       <el-dialog  :visible.sync="imageVisiable" width="60%" append-to-body>
       <el-row>
          <el-col :span="8" :offset="4" class ="jpgCss">
          
              <el-upload
                class="upload-demo"
                ref="upload"
                action="#"
                :multiple="false"
                :show-file-list="true"
                :before-upload="beforeUpload"
                accept=".jpg,.png,"
                :on-preview="handlePreview"
                :on-exceed="handleExceed"
                :on-change="onChange"
                :file-list="fileList"
                :auto-upload="false"
                :limit="10"
              >
              <el-button> <i  class="el-icon-plus avatar-uploader-icon"></i></el-button>
              
              </el-upload>
          </el-col>
          <el-col :span="8" :offset="1">
              <el-button
                style="margin-left: 200px;"
                @click="setUpload"
                type="primary"
                plain
              >
                上传
              </el-button>
          
          </el-col>
        </el-row>
      </el-dialog>
  </div>
</template>

<script>
import {
  ListItem,
  ListCombo,
  SaveExamData,
  RemoveExamData,
  ListContact,
  RemoveContect,
  InsertItems,
  SaveExamImage
} from '@/api/exam/combo'
import env from '@/utils/ApiConfig'
export default {
  //   dicts: ["sys_yes_no"],
  data() {
    return {
        baseUrl: env.get_base_url(),
      comboModel:{ },
      fileList: [],
      imageVisiable:false,
      loading: false,
      value: [],
      title: '',
      open: false,
      itemsList: [],
      contactModel: {},
      currentPage: 1,
      pageSize: 10,
      showclose: false,
      contactOpen: false,
      contactList: [],
      clearFalse: false,
      suitOption: [
        { value: '通用', label: '通用' },
        { value: '男', label: '男' },
        { value: '女', label: '女' },
      ],
      comboList: [],

      queryParams: {},

      // 表单参数
      form: {
        croudSuitable: '通用',
      },
    }
  },
  created() {
    this.getList()
    this.getItems()
    this.baseApi = env.get_base_url()
  },

  methods: {
    addItems() {
      this.loading = true
      if (this.value.length == 0) {
        this.$message({
          message: '请选择项目',
          type: 'warning',
          showClose: true,
        })
        return
      }
      let param = {
        comboId: this.contactModel.id,
        itemId: this.value,
      }

      InsertItems(param).then((res) => {
        if (res.code == 200) {
          this.value = []
          ListContact(this.contactModel).then((res) => {
            if (res.code == 200) {
              this.loading = false
              this.contactList = res.data
              this.$message({
                message: '操作成功',
                type: 'success',
                showClose: true,
              })
            }
          })
        }
      })
    },
    getItems() {
      ListItem(this.queryParams).then((res) => {
        if (res.code === 200) {
          this.itemsList = res.data.rows
        }
      })
    },
    gainListContact(row) {
      this.contactModel = row

      ListContact(row).then((res) => {
        if (res.code == 200) {
          this.contactList = res.data
          this.contactOpen = true
        }
      })
    },
    /** 查询列表 */
    getList() {
      ListCombo(this.queryParams).then((res) => {
        if (res.code === 200) {
          this.comboList = res.data
        }
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {}
      this.resetForm('form')
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset()
      this.thisDisabled = false
      this.open = true
      this.title = '新增'
    },
    itemChange(row) {
      let param = {
        ID: row.id,
        comboName: row.combo_name,
        price: row.price,
        reallPrice: row.reall_price,
        croudSuitable:row.croud_suitable,
      }
      SaveExamData(param).then((res) => {
        if (res.code == 200) {
          this.$message({
            message: '操作成功',
            type: 'success',
            showClose: true,
          })
        }
      })
    },
    /** 提交按钮 */
    submitForm: function () {
      SaveExamData(this.form).then((res) => {
        if (res.code == 200) {
          this.$message({
            message: '操作成功',
            type: 'success',
            showClose: true,
          })
          this.getList()
          this.open = false
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm('是否确认删除检查项目为"' + row.item_name + '"的数据项？')
        .then(function () {
          return RemoveExamData(row)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    delContact(row) {
      this.$modal
        .confirm('是否确认删除检查项目为"' + row.itemName + '"的数据项？')
        .then(function () {
          return RemoveContect(row)
        })
        .then(() => {
          this.loading = true
          ListContact(this.contactModel).then((res) => {
            if (res.code == 200) {
              this.contactList = res.data
              this.loading = false
              this.$modal.msgSuccess('删除成功')
            }
          })
        })
        .catch(() => {})
    },
    toSetImages(row) { 
       this.comboModel=row
      this.imageVisiable = true
      this.fileList=[]
     
    },
    handleCollectClose(done) {
      //汇总关闭
      done()
    },
    clickCollectClose() {
      this.contactOpen = false
    },
    handleClose(done) {
      this.getList()
      done()
    },
    pdfVisiableClose() {},
    clickPdfClose() {
      this.getList()
      this.contactOpen = false
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
    },
    handleSizeChange(val) {
      this.pagesize = val
    },
     //限制文件大小
    beforeUpload(file) {
      const isLt10M = file.size / 1024 < 50

      if (!isLt10M) {
        this.$message.error('上传文件过大 ')
        return false
      }
    },
    //数量提示 共选择了 ${files.length + fileList.length
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前最多选择 10 个文件上传，
        超出文件最大数量限制`,
      )
    },
    // 选取完文件触发事件
    onChange(a, fileList) {
      this.fileList = fileList
    },
  
    handlePreview() { },
    setUpload(row) {
       //如果没有选择文件则不允许点击,并给出提示选择文件后点击上传按钮
      if (this.fileList.length == 0) {
        this.$notify.info({
          title: '提示',
          message: '请选择图片后再点击上传!',
        })
      }
      if (this.fileList.length > 0) {
           //调用接口
        //创建formData对象
        var param = new FormData()
        //将文件append到formData对象

        this.fileList.forEach((item) => {
          param.append('file', item.raw)
          param.append('ID', this.comboModel.id)
          param.append('comboName', this.comboModel.combo_name)
          param.append('price', this.comboModel.price)
          param.append('reallPrice',  this.comboModel.reall_price)
           param.append('croudSuitable', this.comboModel.croud_suitable)        
        })
        SaveExamImage(param).then((res) => {
          if (res.code == 200) {
            this.getList()
            this.imageVisiable = false
            
            this.$message({
              message: '操作成功',
              type: 'success',
              showClose: true,
            })
          } else { 
             this.$message({
              showClose: true,
              message: "网络波动操作失败",
              type: 'msgError',
            })
          }
        })
      } 
    },
  },
}
</script>

<style lang="scss" scoped>
.el-container {
  max-height: 600px;
}

.qrcodeCon {
  text-align: center;
}

.qrcodeUrl {
  padding: 15px 0;
}

.qrcodeDownload {
  text-align: center;
}
.img {
  width: 100px;
  height: auto;
}
.xuanfu {
  position: fixed;
  top: 0;
  right: 0;

  background-color: #ccc;
}

</style>
